# I Wanna Be The GMS2_3 Engine YoYoYo Edition

This is the GMS2.3 version of YoYoYoDude's I wanna engine, which fixes physical problems and makes some improvements.

## Change Log

- Fixed the problem of getting stuck when saving
- Fixed getting stuck when touching the corners of the block
- Fixed the death if standing in the middle of spike and block
- Fixed getting stuck in the block when touching the gravity arrow
- Fixed the player sprite switching too fast without moving, now it is consistent with GMS1
- Set the correct parent object for objWarpNextAutosave
- Regroup the scripts to make them more reasonable
- Use correct Js Doc syntax to write comments for scripts

