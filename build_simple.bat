@echo off
echo ========================================
echo Whisper GMS2 Extension 简化构建脚本
echo ========================================
echo.
echo 注意: 此脚本需要以下工具:
echo - Visual Studio 2019/2022 (带C++工具)
echo - CMake 3.16+
echo - Git
echo.
echo 如果您没有这些工具，请:
echo 1. 安装Visual Studio Community (免费)
echo 2. 安装CMake
echo 3. 确保whisper.cpp库在上级目录
echo.

:: 检查whisper.cpp
if not exist "..\whisper.cpp\include\whisper.h" (
    echo 错误: 未找到whisper.cpp库
    echo 请确保whisper.cpp目录存在于上级目录中
    echo.
    echo 您可以通过以下命令获取:
    echo git clone https://github.com/ggerganov/whisper.cpp.git
    pause
    exit /b 1
)

:: 创建构建目录
if not exist build mkdir build
cd build

echo 正在配置项目...
echo.

:: 尝试使用CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到CMake
    echo 请安装CMake或使用Visual Studio Developer Command Prompt
    echo.
    goto :manual_build
)

:: 使用CMake构建
cmake .. -G "Visual Studio 17 2022" -A x64
if errorlevel 1 (
    echo CMake配置失败，尝试其他生成器...
    cmake .. -G "Visual Studio 16 2019" -A x64
    if errorlevel 1 (
        echo CMake配置失败
        goto :manual_build
    )
)

cmake --build . --config Release
if errorlevel 1 (
    echo 构建失败
    goto :manual_build
)

echo.
echo 构建成功!
echo DLL文件位置: build\Release\whisper_gms2.dll
goto :end

:manual_build
echo.
echo ========================================
echo 手动构建说明
echo ========================================
echo.
echo 由于缺少构建工具，请按以下步骤手动构建:
echo.
echo 1. 安装Visual Studio Community 2022
echo    - 包含"使用C++的桌面开发"工作负载
echo.
echo 2. 安装CMake
echo    - 从 https://cmake.org/download/ 下载
echo.
echo 3. 克隆whisper.cpp库
echo    git clone https://github.com/ggerganov/whisper.cpp.git
echo.
echo 4. 在Visual Studio Developer Command Prompt中运行:
echo    cd whisper_gms2_extension
echo    mkdir build ^&^& cd build
echo    cmake .. -G "Visual Studio 17 2022" -A x64
echo    cmake --build . --config Release
echo.

:end
cd ..
echo.
echo 构建脚本执行完成
pause
