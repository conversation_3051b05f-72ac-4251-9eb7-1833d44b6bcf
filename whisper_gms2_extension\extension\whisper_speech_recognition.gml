/// @description Whisper语音识别扩展 - GML封装脚本
/// <AUTHOR> GMS2 Extension

// 语言常量定义
#macro WHISPER_LANG_AUTO 0
#macro WHISPER_LANG_CHINESE 1
#macro WHISPER_LANG_ENGLISH 2

// 错误代码常量
#macro WHISPER_SUCCESS 0
#macro WHISPER_ERROR_INIT -1
#macro WHISPER_ERROR_INVALID_PARAM -2
#macro WHISPER_ERROR_AUDIO -3
#macro WHISPER_ERROR_PROCESSING -4
#macro WHISPER_ERROR_MODEL -5

// 全局变量
global.whisper_initialized = false;
global.whisper_capturing = false;
global.whisper_last_result = "";
global.whisper_last_confidence = 0;
global.whisper_current_language = WHISPER_LANG_AUTO;

/// @function whisper_init
/// @description 初始化Whisper语音识别引擎
/// @param {string} model_path 模型文件路径
/// @param {real} language 语言设置 (可选，默认为自动检测)
/// @param {real} n_threads 线程数 (可选，默认为0自动检测)
/// @return {bool} 成功返回true，失败返回false
function whisper_init(model_path, language = WHISPER_LANG_AUTO, n_threads = 0) {
    if (global.whisper_initialized) {
        show_debug_message("Whisper: 引擎已经初始化");
        return true;
    }
    
    var result = whisper_gms2_init(model_path, language, n_threads);
    
    if (result == WHISPER_SUCCESS) {
        global.whisper_initialized = true;
        global.whisper_current_language = language;
        show_debug_message("Whisper: 引擎初始化成功");
        return true;
    } else {
        var error_msg = whisper_gms2_get_last_error();
        show_debug_message("Whisper: 初始化失败 - " + error_msg);
        return false;
    }
}

/// @function whisper_cleanup
/// @description 清理Whisper语音识别引擎资源
/// @return {bool} 成功返回true
function whisper_cleanup() {
    if (!global.whisper_initialized) {
        return true;
    }
    
    // 停止捕获
    if (global.whisper_capturing) {
        whisper_stop_capture();
    }
    
    var result = whisper_gms2_free();
    global.whisper_initialized = false;
    global.whisper_capturing = false;
    
    show_debug_message("Whisper: 引擎资源已清理");
    return (result == WHISPER_SUCCESS);
}

/// @function whisper_start_capture
/// @description 开始实时语音捕获
/// @param {real} device_id 音频设备ID (可选，默认为-1使用默认设备)
/// @return {bool} 成功返回true，失败返回false
function whisper_start_capture(device_id = -1) {
    if (!global.whisper_initialized) {
        show_debug_message("Whisper: 引擎未初始化");
        return false;
    }
    
    if (global.whisper_capturing) {
        show_debug_message("Whisper: 音频捕获已在运行");
        return true;
    }
    
    var result = whisper_gms2_start_capture(device_id);
    
    if (result == WHISPER_SUCCESS) {
        global.whisper_capturing = true;
        show_debug_message("Whisper: 音频捕获已开始");
        return true;
    } else {
        var error_msg = whisper_gms2_get_last_error();
        show_debug_message("Whisper: 音频捕获启动失败 - " + error_msg);
        return false;
    }
}

/// @function whisper_stop_capture
/// @description 停止实时语音捕获
/// @return {bool} 成功返回true
function whisper_stop_capture() {
    if (!global.whisper_capturing) {
        return true;
    }
    
    var result = whisper_gms2_stop_capture();
    global.whisper_capturing = false;
    
    show_debug_message("Whisper: 音频捕获已停止");
    return (result == WHISPER_SUCCESS);
}

/// @function whisper_get_result
/// @description 获取最新的语音识别结果
/// @return {string} 识别文本，无结果返回空字符串
function whisper_get_result() {
    if (!global.whisper_initialized) {
        return "";
    }
    
    var result = whisper_gms2_get_result();
    if (result != undefined && result != "") {
        global.whisper_last_result = result;
        global.whisper_last_confidence = whisper_gms2_get_confidence();
    }
    
    return result;
}

/// @function whisper_get_confidence
/// @description 获取最新识别结果的置信度
/// @return {real} 置信度 (0-100)
function whisper_get_confidence() {
    if (!global.whisper_initialized) {
        return 0;
    }
    
    return whisper_gms2_get_confidence();
}

/// @function whisper_clear_result
/// @description 清除当前识别结果
/// @return {bool} 成功返回true
function whisper_clear_result() {
    if (!global.whisper_initialized) {
        return false;
    }
    
    global.whisper_last_result = "";
    global.whisper_last_confidence = 0;
    
    var result = whisper_gms2_clear_result();
    return (result == WHISPER_SUCCESS);
}

/// @function whisper_set_language
/// @description 设置识别语言
/// @param {real} language 语言代码 (WHISPER_LANG_AUTO, WHISPER_LANG_CHINESE, WHISPER_LANG_ENGLISH)
/// @return {bool} 成功返回true
function whisper_set_language(language) {
    if (!global.whisper_initialized) {
        show_debug_message("Whisper: 引擎未初始化");
        return false;
    }
    
    var result = whisper_gms2_set_language(language);
    
    if (result == WHISPER_SUCCESS) {
        global.whisper_current_language = language;
        var lang_name = "";
        switch (language) {
            case WHISPER_LANG_CHINESE: lang_name = "中文"; break;
            case WHISPER_LANG_ENGLISH: lang_name = "英文"; break;
            default: lang_name = "自动检测"; break;
        }
        show_debug_message("Whisper: 语言已设置为 " + lang_name);
        return true;
    } else {
        var error_msg = whisper_gms2_get_last_error();
        show_debug_message("Whisper: 语言设置失败 - " + error_msg);
        return false;
    }
}

/// @function whisper_get_language
/// @description 获取当前语言设置
/// @return {real} 当前语言代码
function whisper_get_language() {
    return global.whisper_current_language;
}

/// @function whisper_get_language_name
/// @description 获取当前语言名称
/// @return {string} 语言名称
function whisper_get_language_name() {
    switch (global.whisper_current_language) {
        case WHISPER_LANG_CHINESE: return "中文";
        case WHISPER_LANG_ENGLISH: return "英文";
        default: return "自动检测";
    }
}

/// @function whisper_is_initialized
/// @description 检查引擎是否已初始化
/// @return {bool} 已初始化返回true
function whisper_is_initialized() {
    return global.whisper_initialized;
}

/// @function whisper_is_capturing
/// @description 检查是否正在捕获音频
/// @return {bool} 正在捕获返回true
function whisper_is_capturing() {
    return global.whisper_capturing;
}

/// @function whisper_get_version
/// @description 获取扩展版本信息
/// @return {string} 版本字符串
function whisper_get_version() {
    return whisper_gms2_get_version();
}

/// @function whisper_get_last_error
/// @description 获取最后的错误信息
/// @return {string} 错误信息
function whisper_get_last_error() {
    return whisper_gms2_get_last_error();
}

/// @function whisper_get_audio_devices
/// @description 获取可用的音频设备列表
/// @return {array} 设备信息数组，每个元素包含 {id, name}
function whisper_get_audio_devices() {
    var device_count = whisper_gms2_get_audio_device_count();
    var devices = [];
    
    for (var i = 0; i < device_count; i++) {
        var device_name = whisper_gms2_get_audio_device_name(i);
        if (device_name != "") {
            array_push(devices, {
                id: i,
                name: device_name
            });
        }
    }
    
    return devices;
}

/// @function whisper_enable_debug
/// @description 启用调试模式
/// @param {bool} enable 是否启用调试
/// @return {bool} 成功返回true
function whisper_enable_debug(enable = true) {
    var result = whisper_gms2_set_debug_mode(enable ? 1 : 0);
    return (result == WHISPER_SUCCESS);
}

/// @function whisper_process_step
/// @description 在Step事件中调用此函数来处理语音识别结果
/// @return {string} 如果有新的识别结果返回文本，否则返回空字符串
function whisper_process_step() {
    if (!global.whisper_initialized || !global.whisper_capturing) {
        return "";
    }
    
    var result = whisper_get_result();
    if (result != "" && result != global.whisper_last_result) {
        global.whisper_last_result = result;
        global.whisper_last_confidence = whisper_get_confidence();
        return result;
    }
    
    return "";
}
