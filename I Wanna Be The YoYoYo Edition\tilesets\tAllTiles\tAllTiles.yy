{
  "spriteId": {
    "name": "sprAllTiles",
    "path": "sprites/sprAllTiles/sprAllTiles.yy",
  },
  "tileWidth": 32,
  "tileHeight": 32,
  "tilexoff": 0,
  "tileyoff": 0,
  "tilehsep": 0,
  "tilevsep": 0,
  "spriteNoExport": true,
  "textureGroupId": {
    "name": "Default",
    "path": "texturegroups/Default",
  },
  "out_tilehborder": 2,
  "out_tilevborder": 2,
  "out_columns": 4,
  "tile_count": 20,
  "autoTileSets": [
    {"tiles":[
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        1,
        1,
        1,
        1,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        2,
        1,
        1,
        1,
        1,
        1,
        2,
        2,
        2,
        2,
        1,
        1,
        2,
        1,
        1,
      ],"closed_edge":false,"resourceVersion":"1.0","name":"Brown_blocks","tags":[],"resourceType":"GMAutoTileSet",},
    {"tiles":[
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        11,
        11,
        11,
        11,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        12,
        11,
        11,
        11,
        11,
        11,
        12,
        12,
        12,
        12,
        11,
        11,
        12,
        11,
        11,
      ],"closed_edge":false,"resourceVersion":"1.0","name":"Red_blocks","tags":[],"resourceType":"GMAutoTileSet",},
    {"tiles":[
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        3,
        3,
        3,
        3,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        4,
        3,
        3,
        3,
        3,
        3,
        4,
        4,
        4,
        4,
        3,
        3,
        4,
        3,
        3,
      ],"closed_edge":false,"resourceVersion":"1.0","name":"Grey_blocks","tags":[],"resourceType":"GMAutoTileSet",},
    {"tiles":[
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        14,
        19,
        19,
        19,
        18,
        19,
        19,
        19,
        14,
        19,
        19,
        19,
        18,
        19,
        19,
        19,
        14,
        18,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
        19,
      ],"closed_edge":false,"resourceVersion":"1.0","name":"Metal_blocks","tags":[],"resourceType":"GMAutoTileSet",},
  ],
  "tileAnimationFrames": [],
  "tileAnimationSpeed": 15.0,
  "tileAnimation": {
    "FrameData": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
    ],
    "SerialiseFrameCount": 1,
  },
  "macroPageTiles": {
    "SerialiseWidth": 0,
    "SerialiseHeight": 0,
    "TileSerialiseData": [],
  },
  "parent": {
    "name": "Tile Sets",
    "path": "folders/Tile Sets.yy",
  },
  "resourceVersion": "1.0",
  "name": "tAllTiles",
  "tags": [],
  "resourceType": "GMTileSet",
}