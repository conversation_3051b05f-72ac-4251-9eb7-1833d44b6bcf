#include "whisper_gms2.h"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <cstdlib>

// 回调函数
void result_callback(const char* text, int confidence, void* user_data) {
    std::cout << "识别结果: " << text << " (置信度: " << confidence << "%)" << std::endl;
}

void error_callback(int error_code, const char* error_message, void* user_data) {
    std::cout << "错误 [" << error_code << "]: " << error_message << std::endl;
}

void debug_callback(const char* debug_message, void* user_data) {
    std::cout << "调试: " << debug_message << std::endl;
}

int main(int argc, char* argv[]) {
    std::cout << "========================================" << std::endl;
    std::cout << "Whisper GMS2 Extension 基础测试程序" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // 检查命令行参数
    std::string model_path = "../../ggml-medium.bin";
    if (argc > 1) {
        model_path = argv[1];
    }
    
    std::cout << "使用模型文件: " << model_path << std::endl;
    
    // 获取版本信息
    std::cout << "DLL版本: " << whisper_gms2_get_version() << std::endl;
    
    // 设置回调函数
    whisper_gms2_set_result_callback(result_callback, nullptr);
    whisper_gms2_set_error_callback(error_callback, nullptr);
    whisper_gms2_set_debug_callback(debug_callback, nullptr);
    
    // 启用调试模式
    whisper_gms2_set_debug_mode(1);
    
    // 初始化引擎
    std::cout << "\n正在初始化Whisper引擎..." << std::endl;
    int result = whisper_gms2_init(model_path.c_str(), WHISPER_GMS2_LANG_CHINESE, 0);
    
    if (result != WHISPER_GMS2_SUCCESS) {
        std::cout << "初始化失败: " << whisper_gms2_get_last_error() << std::endl;
        return 1;
    }
    
    std::cout << "引擎初始化成功!" << std::endl;
    
    // 检查初始化状态
    if (whisper_gms2_is_initialized()) {
        std::cout << "引擎状态: 已初始化" << std::endl;
    }
    
    // 获取音频设备信息
    int device_count = whisper_gms2_get_audio_device_count();
    std::cout << "\n发现 " << device_count << " 个音频设备:" << std::endl;
    
    for (int i = 0; i < device_count; ++i) {
        const char* device_name = whisper_gms2_get_audio_device_name(i);
        if (device_name) {
            std::cout << "  设备 " << i << ": " << device_name << std::endl;
        }
    }
    
    // 设置音频参数
    std::cout << "\n设置音频参数..." << std::endl;
    whisper_gms2_set_audio_params(16000, 1);
    whisper_gms2_set_processing_params(0.6f, 0.6f);
    
    // 测试语言设置
    std::cout << "当前语言: " << whisper_gms2_get_language() << std::endl;
    
    whisper_gms2_set_language(WHISPER_GMS2_LANG_ENGLISH);
    std::cout << "切换到英文，当前语言: " << whisper_gms2_get_language() << std::endl;
    
    whisper_gms2_set_language(WHISPER_GMS2_LANG_CHINESE);
    std::cout << "切换到中文，当前语言: " << whisper_gms2_get_language() << std::endl;
    
    // 开始音频捕获测试
    std::cout << "\n开始音频捕获测试..." << std::endl;
    std::cout << "请说话，程序将运行10秒钟..." << std::endl;
    
    result = whisper_gms2_start_capture(-1); // 使用默认设备
    if (result != WHISPER_GMS2_SUCCESS) {
        std::cout << "音频捕获启动失败: " << whisper_gms2_get_last_error() << std::endl;
    } else {
        std::cout << "音频捕获已开始" << std::endl;
        
        // 运行10秒
        for (int i = 0; i < 10; ++i) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            std::cout << "." << std::flush;
            
            // 检查是否有识别结果
            const char* text = whisper_gms2_get_result();
            if (text && strlen(text) > 0) {
                int confidence = whisper_gms2_get_confidence();
                int start_ms, end_ms;
                whisper_gms2_get_timestamp(&start_ms, &end_ms);
                
                std::cout << "\n最新结果: " << text 
                         << " (置信度: " << confidence << "%, "
                         << "时间: " << start_ms << "-" << end_ms << "ms)" << std::endl;
                
                // 清除结果
                whisper_gms2_clear_result();
            }
        }
        
        std::cout << "\n停止音频捕获..." << std::endl;
        whisper_gms2_stop_capture();
    }
    
    // 测试手动音频处理
    std::cout << "\n测试手动音频处理..." << std::endl;
    
    // 生成测试音频数据 (简单的正弦波)
    const int sample_rate = 16000;
    const int duration_seconds = 2;
    const int sample_count = sample_rate * duration_seconds;
    
    std::vector<float> test_audio(sample_count);
    for (int i = 0; i < sample_count; ++i) {
        // 生成440Hz的正弦波 (A音符)
        test_audio[i] = 0.3f * sin(2.0f * 3.14159f * 440.0f * i / sample_rate);
    }
    
    result = whisper_gms2_process_audio(test_audio.data(), sample_count);
    if (result == WHISPER_GMS2_SUCCESS) {
        std::cout << "测试音频处理完成" << std::endl;
    } else {
        std::cout << "测试音频处理失败: " << whisper_gms2_get_last_error() << std::endl;
    }
    
    // 清理
    std::cout << "\n清理资源..." << std::endl;
    whisper_gms2_free();
    
    std::cout << "测试完成!" << std::endl;
    
    return 0;
}
