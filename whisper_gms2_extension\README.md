# Whisper GMS2 Extension

基于OpenAI Whisper C++库的GameMaker Studio 2实时语音识别扩展，专为I Wanna类游戏开发设计。

## 功能特性

- ✅ **实时语音识别**: 支持实时音频捕获和语音转文字
- ✅ **多语言支持**: 支持中文、英文和自动语言检测
- ✅ **高精度识别**: 基于OpenAI Whisper模型，识别准确率高
- ✅ **简单易用**: 提供友好的GML接口，易于集成到游戏中
- ✅ **音频设备管理**: 支持多音频设备选择和管理
- ✅ **错误处理**: 完善的错误处理和调试支持
- ✅ **跨平台**: 支持Windows平台（未来可扩展到其他平台）

## 系统要求

- **操作系统**: Windows 10/11 (64位)
- **GameMaker Studio**: 2022.1 或更高版本
- **运行时**: Visual C++ Redistributable 2019 或更高版本
- **内存**: 建议4GB以上RAM
- **存储**: 模型文件需要约1-3GB空间

## 快速开始

### 1. 安装扩展

1. 将 `whisper_gms2.dll` 复制到你的GameMaker项目的扩展目录
2. 在GameMaker Studio中导入 `whisper_gms2.yy` 扩展文件
3. 下载Whisper模型文件（推荐使用 `ggml-medium.bin`）

### 2. 基本使用

```gml
// 在游戏开始时初始化
if (whisper_init("models/ggml-medium.bin", WHISPER_LANG_CHINESE)) {
    show_debug_message("语音识别引擎初始化成功!");
    
    // 开始音频捕获
    whisper_start_capture();
} else {
    show_debug_message("初始化失败: " + whisper_get_last_error());
}

// 在Step事件中检查识别结果
var speech_result = whisper_process_step();
if (speech_result != "") {
    show_debug_message("识别结果: " + speech_result);
    show_debug_message("置信度: " + string(whisper_get_confidence()) + "%");
    
    // 处理语音命令
    if (string_pos("跳跃", speech_result) > 0) {
        // 执行跳跃动作
        player_jump();
    }
    
    // 清除结果
    whisper_clear_result();
}

// 游戏结束时清理资源
whisper_cleanup();
```

### 3. 高级用法

#### 语言切换
```gml
// 切换到英文识别
whisper_set_language(WHISPER_LANG_ENGLISH);

// 切换到中文识别
whisper_set_language(WHISPER_LANG_CHINESE);

// 自动语言检测
whisper_set_language(WHISPER_LANG_AUTO);
```

#### 音频设备管理
```gml
// 获取可用音频设备
var devices = whisper_get_audio_devices();
for (var i = 0; i < array_length(devices); i++) {
    show_debug_message("设备 " + string(devices[i].id) + ": " + devices[i].name);
}

// 使用特定设备
whisper_start_capture(1); // 使用设备ID为1的音频设备
```

#### 调试模式
```gml
// 启用调试模式
whisper_enable_debug(true);

// 获取详细错误信息
if (!whisper_is_initialized()) {
    show_debug_message("错误: " + whisper_get_last_error());
}
```

## API参考

### 核心函数

| 函数 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `whisper_init(model_path, language, n_threads)` | 初始化引擎 | 模型路径, 语言, 线程数 | bool |
| `whisper_cleanup()` | 清理资源 | 无 | bool |
| `whisper_start_capture(device_id)` | 开始音频捕获 | 设备ID | bool |
| `whisper_stop_capture()` | 停止音频捕获 | 无 | bool |
| `whisper_get_result()` | 获取识别结果 | 无 | string |
| `whisper_get_confidence()` | 获取置信度 | 无 | real |
| `whisper_clear_result()` | 清除结果 | 无 | bool |

### 配置函数

| 函数 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `whisper_set_language(language)` | 设置语言 | 语言代码 | bool |
| `whisper_get_language()` | 获取当前语言 | 无 | real |
| `whisper_get_language_name()` | 获取语言名称 | 无 | string |
| `whisper_enable_debug(enable)` | 启用调试 | 是否启用 | bool |

### 状态查询函数

| 函数 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `whisper_is_initialized()` | 检查初始化状态 | 无 | bool |
| `whisper_is_capturing()` | 检查捕获状态 | 无 | bool |
| `whisper_get_version()` | 获取版本信息 | 无 | string |
| `whisper_get_last_error()` | 获取错误信息 | 无 | string |
| `whisper_get_audio_devices()` | 获取音频设备列表 | 无 | array |

### 常量定义

```gml
// 语言常量
#macro WHISPER_LANG_AUTO 0      // 自动检测
#macro WHISPER_LANG_CHINESE 1   // 中文
#macro WHISPER_LANG_ENGLISH 2   // 英文

// 错误代码
#macro WHISPER_SUCCESS 0                // 成功
#macro WHISPER_ERROR_INIT -1           // 初始化错误
#macro WHISPER_ERROR_INVALID_PARAM -2  // 参数错误
#macro WHISPER_ERROR_AUDIO -3          // 音频错误
#macro WHISPER_ERROR_PROCESSING -4     // 处理错误
#macro WHISPER_ERROR_MODEL -5          // 模型错误
```

## 模型文件

### 推荐模型

| 模型 | 大小 | 语言支持 | 精度 | 速度 | 推荐用途 |
|------|------|----------|------|------|----------|
| `ggml-tiny.bin` | ~39MB | 多语言 | 低 | 快 | 测试开发 |
| `ggml-base.bin` | ~142MB | 多语言 | 中 | 中 | 轻量应用 |
| `ggml-small.bin` | ~466MB | 多语言 | 中高 | 中 | 一般应用 |
| `ggml-medium.bin` | ~1.5GB | 多语言 | 高 | 慢 | 高精度需求 |
| `ggml-large.bin` | ~2.9GB | 多语言 | 最高 | 最慢 | 专业应用 |

### 下载地址

- [Hugging Face](https://huggingface.co/ggerganov/whisper.cpp)
- [GitHub Releases](https://github.com/ggerganov/whisper.cpp/releases)

## 构建说明

### 环境要求

- Visual Studio 2019/2022
- CMake 3.16+
- Git

### 构建步骤

1. **克隆仓库**
```bash
git clone --recursive https://github.com/your-repo/whisper-gms2-extension.git
cd whisper-gms2-extension
```

2. **运行构建脚本**
```bash
# 发布版本
build.bat

# 调试版本
build.bat --debug

# 启用CUDA支持
build.bat --cuda

# 清理重新构建
build.bat --clean
```

3. **输出文件**
- `build/Release/whisper_gms2.dll` - 主DLL文件
- `build/Release/whisper_gms2.lib` - 导入库文件

## 性能优化

### 硬件加速

```bash
# 启用CUDA支持（需要NVIDIA GPU）
build.bat --cuda

# 启用OpenCL支持
build.bat --opencl
```

### 参数调优

```gml
// 设置合适的线程数（通常为CPU核心数）
whisper_init("model.bin", WHISPER_LANG_CHINESE, 4);

// 选择合适的模型大小
// 游戏中推荐使用 small 或 base 模型以平衡精度和性能
```

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查模型文件路径是否正确
   - 确保模型文件完整下载
   - 检查系统内存是否足够

2. **音频捕获失败**
   - 检查麦克风权限
   - 确认音频设备正常工作
   - 尝试使用不同的音频设备

3. **识别精度低**
   - 使用更大的模型文件
   - 确保环境安静
   - 调整麦克风音量

4. **性能问题**
   - 使用较小的模型
   - 启用硬件加速
   - 调整线程数设置

### 调试技巧

```gml
// 启用详细调试信息
whisper_enable_debug(true);

// 检查系统状态
show_debug_message("版本: " + whisper_get_version());
show_debug_message("初始化状态: " + string(whisper_is_initialized()));
show_debug_message("捕获状态: " + string(whisper_is_capturing()));

// 监控错误信息
var error = whisper_get_last_error();
if (error != "无错误") {
    show_debug_message("错误: " + error);
}
```

## 许可证

本项目基于MIT许可证开源。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 致谢

- [OpenAI Whisper](https://github.com/openai/whisper) - 强大的语音识别模型
- [whisper.cpp](https://github.com/ggerganov/whisper.cpp) - 高效的C++实现
- GameMaker Studio 2 社区的支持和反馈
