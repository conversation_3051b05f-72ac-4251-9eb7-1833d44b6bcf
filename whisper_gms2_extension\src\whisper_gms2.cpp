#define WHISPER_GMS2_EXPORTS
#include "whisper_gms2_internal.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <cstring>
#include <cmath>

// 全局变量定义
std::unique_ptr<WhisperGMS2Engine> g_engine = nullptr;
std::mutex g_engine_mutex;

// ============================================================================
// AudioBuffer实现
// ============================================================================

void AudioBuffer::write(const float* samples, size_t count) {
    std::lock_guard<std::mutex> lock(mutex);
    
    for (size_t i = 0; i < count; ++i) {
        data[write_pos] = samples[i];
        write_pos = (write_pos + 1) % data.size();
        
        // 如果缓冲区满了，覆盖最旧的数据
        if (write_pos == read_pos) {
            read_pos = (read_pos + 1) % data.size();
        }
    }
}

size_t AudioBuffer::read(float* samples, size_t count) {
    std::lock_guard<std::mutex> lock(mutex);
    
    size_t available_samples = available();
    size_t samples_to_read = (count < available_samples) ? count : available_samples;
    
    for (size_t i = 0; i < samples_to_read; ++i) {
        samples[i] = data[read_pos];
        read_pos = (read_pos + 1) % data.size();
    }
    
    return samples_to_read;
}

size_t AudioBuffer::available() const {
    if (write_pos >= read_pos) {
        return write_pos - read_pos;
    } else {
        return data.size() - read_pos + write_pos;
    }
}

void AudioBuffer::clear() {
    std::lock_guard<std::mutex> lock(mutex);
    write_pos = 0;
    read_pos = 0;
    std::fill(data.begin(), data.end(), 0.0f);
}

// ============================================================================
// WhisperGMS2Engine构造函数和析构函数
// ============================================================================

WhisperGMS2Engine::WhisperGMS2Engine() 
    : whisper_ctx(nullptr)
    , initialized(false)
    , capturing(false)
    , processing(false)
    , should_stop(false)
    , current_language(WHISPER_GMS2_LANG_CHINESE)
    , sample_rate(WHISPER_GMS2_SAMPLE_RATE)
    , channels(WHISPER_GMS2_CHANNELS)
    , n_threads(0)
    , vad_threshold(0.6f)
    , no_speech_threshold(0.6f)
    , debug_mode(false)
    , current_device_id(-1)
    , result_callback(nullptr)
    , result_callback_data(nullptr)
    , error_callback(nullptr)
    , error_callback_data(nullptr)
    , debug_callback(nullptr)
    , debug_callback_data(nullptr)
{
#ifdef _WIN32
    wave_in_handle = nullptr;
    memset(wave_headers, 0, sizeof(wave_headers));
#endif
    
    audio_buffer = std::make_unique<AudioBuffer>(WHISPER_GMS2_AUDIO_BUFFER_SIZE);
    init_audio_devices();
}

WhisperGMS2Engine::~WhisperGMS2Engine() {
    shutdown();
}

// ============================================================================
// 核心初始化和清理函数
// ============================================================================

int WhisperGMS2Engine::initialize(const char* model_path, int language, int n_threads) {
    std::lock_guard<std::mutex> lock(g_engine_mutex);
    
    if (initialized.load()) {
        debug_log("引擎已经初始化");
        return WHISPER_GMS2_SUCCESS;
    }
    
    try {
        // 检查模型文件是否存在
        std::ifstream model_file(model_path, std::ios::binary);
        if (!model_file.good()) {
            set_error(WHISPER_GMS2_ERROR_MODEL, "无法打开模型文件: " + std::string(model_path));
            return WHISPER_GMS2_ERROR_MODEL;
        }
        model_file.close();
        
        debug_log("正在加载Whisper模型: " + std::string(model_path));
        
        // 初始化Whisper上下文参数
        struct whisper_context_params cparams = whisper_context_default_params();
        cparams.use_gpu = true;  // 尝试使用GPU加速
        
        // 加载Whisper模型
        whisper_ctx = whisper_init_from_file_with_params(model_path, cparams);
        if (!whisper_ctx) {
            set_error(WHISPER_GMS2_ERROR_MODEL, "无法初始化Whisper模型");
            return WHISPER_GMS2_ERROR_MODEL;
        }
        
        // 设置参数
        current_language = language;
        this->n_threads = (n_threads <= 0) ? std::thread::hardware_concurrency() : n_threads;
        
        // 初始化Whisper参数
        init_whisper_params();
        
        initialized.store(true);
        debug_log("Whisper引擎初始化成功");
        
        return WHISPER_GMS2_SUCCESS;
        
    } catch (const std::exception& e) {
        set_error(WHISPER_GMS2_ERROR_INIT, "初始化失败: " + std::string(e.what()));
        return WHISPER_GMS2_ERROR_INIT;
    }
}

int WhisperGMS2Engine::shutdown() {
    std::lock_guard<std::mutex> lock(g_engine_mutex);
    
    if (!initialized.load()) {
        return WHISPER_GMS2_SUCCESS;
    }
    
    debug_log("正在关闭Whisper引擎");
    
    // 停止音频捕获
    stop_audio_capture();
    
    // 设置停止标志
    should_stop.store(true);
    
    // 等待处理线程结束
    if (processing_thread.joinable()) {
        processing_thread.join();
    }
    
    // 清理Whisper资源
    if (whisper_ctx) {
        whisper_free(whisper_ctx);
        whisper_ctx = nullptr;
    }
    
    // 清理音频缓冲区
    if (audio_buffer) {
        audio_buffer->clear();
    }
    
    // 重置状态
    initialized.store(false);
    processing.store(false);
    should_stop.store(false);
    
    debug_log("Whisper引擎已关闭");
    
    return WHISPER_GMS2_SUCCESS;
}

// ============================================================================
// Whisper参数初始化
// ============================================================================

void WhisperGMS2Engine::init_whisper_params() {
    whisper_params = whisper_full_default_params(WHISPER_SAMPLING_GREEDY);
    
    // 基本参数
    whisper_params.n_threads = n_threads;
    whisper_params.offset_ms = 0;
    whisper_params.duration_ms = 0;
    
    // 语言设置
    switch (current_language) {
        case WHISPER_GMS2_LANG_CHINESE:
            whisper_params.language = "zh";
            break;
        case WHISPER_GMS2_LANG_ENGLISH:
            whisper_params.language = "en";
            break;
        default:
            whisper_params.language = nullptr; // 自动检测
            break;
    }
    
    // 输出控制
    whisper_params.translate = false;
    whisper_params.no_context = true;
    whisper_params.no_timestamps = false;
    whisper_params.single_segment = false;
    whisper_params.print_special = false;
    whisper_params.print_progress = debug_mode;
    whisper_params.print_realtime = false;
    whisper_params.print_timestamps = false;
    
    // 质量参数
    whisper_params.suppress_blank = true;
    whisper_params.suppress_nst = true;
    whisper_params.temperature = 0.0f;
    whisper_params.max_initial_ts = 1.0f;
    whisper_params.length_penalty = -1.0f;
    
    // 阈值参数
    whisper_params.entropy_thold = 2.4f;
    whisper_params.logprob_thold = -1.0f;
    whisper_params.no_speech_thold = no_speech_threshold;
    
    // 高级参数
    whisper_params.greedy.best_of = 1;
    whisper_params.audio_ctx = 0;
    whisper_params.debug_mode = debug_mode;
}

// ============================================================================
// 错误处理和调试
// ============================================================================

void WhisperGMS2Engine::set_error(int error_code, const std::string& message) {
    std::lock_guard<std::mutex> lock(error_mutex);
    last_error = message;
    
    debug_log("错误 [" + std::to_string(error_code) + "]: " + message);
    
    if (error_callback) {
        error_callback(error_code, message.c_str(), error_callback_data);
    }
}

void WhisperGMS2Engine::debug_log(const std::string& message) {
    if (debug_mode) {
        std::string log_msg = "[WhisperGMS2] " + message;
        
        if (debug_callback) {
            debug_callback(log_msg.c_str(), debug_callback_data);
        } else {
            std::cout << log_msg << std::endl;
        }
    }
}

std::string WhisperGMS2Engine::get_last_error() {
    std::lock_guard<std::mutex> lock(error_mutex);
    return last_error;
}

// ============================================================================
// 配置函数
// ============================================================================

int WhisperGMS2Engine::set_language(int language) {
    if (language < WHISPER_GMS2_LANG_AUTO || language > WHISPER_GMS2_LANG_ENGLISH) {
        set_error(WHISPER_GMS2_ERROR_INVALID_PARAM, "无效的语言代码");
        return WHISPER_GMS2_ERROR_INVALID_PARAM;
    }
    
    current_language = language;
    
    if (initialized.load()) {
        init_whisper_params();
    }
    
    debug_log("语言设置已更改为: " + std::string(language_code_to_string(language)));
    
    return WHISPER_GMS2_SUCCESS;
}

int WhisperGMS2Engine::set_audio_params(int sample_rate, int channels) {
    if (sample_rate <= 0 || channels <= 0) {
        set_error(WHISPER_GMS2_ERROR_INVALID_PARAM, "无效的音频参数");
        return WHISPER_GMS2_ERROR_INVALID_PARAM;
    }
    
    this->sample_rate = sample_rate;
    this->channels = channels;
    
    debug_log("音频参数已设置: " + std::to_string(sample_rate) + "Hz, " + 
              std::to_string(channels) + " 声道");
    
    return WHISPER_GMS2_SUCCESS;
}

int WhisperGMS2Engine::set_processing_params(float vad_threshold, float no_speech_threshold) {
    if (vad_threshold < 0.0f || vad_threshold > 1.0f ||
        no_speech_threshold < 0.0f || no_speech_threshold > 1.0f) {
        set_error(WHISPER_GMS2_ERROR_INVALID_PARAM, "阈值参数必须在0.0-1.0范围内");
        return WHISPER_GMS2_ERROR_INVALID_PARAM;
    }
    
    this->vad_threshold = vad_threshold;
    this->no_speech_threshold = no_speech_threshold;
    
    if (initialized.load()) {
        init_whisper_params();
    }
    
    debug_log("处理参数已设置: VAD阈值=" + std::to_string(vad_threshold) + 
              ", 静音阈值=" + std::to_string(no_speech_threshold));
    
    return WHISPER_GMS2_SUCCESS;
}

int WhisperGMS2Engine::set_debug_mode(bool enable) {
    debug_mode = enable;

    if (initialized.load()) {
        init_whisper_params();
    }

    debug_log("调试模式" + std::string(enable ? "已启用" : "已禁用"));

    return WHISPER_GMS2_SUCCESS;
}

// ============================================================================
// 音频设备管理
// ============================================================================

void WhisperGMS2Engine::init_audio_devices() {
    audio_devices.clear();

#ifdef _WIN32
    UINT num_devices = waveInGetNumDevs();

    for (UINT i = 0; i < num_devices; ++i) {
        WAVEINCAPS caps;
        if (waveInGetDevCaps(i, &caps, sizeof(caps)) == MMSYSERR_NOERROR) {
            AudioDeviceInfo device;
            device.id = static_cast<int>(i);

            // 转换设备名称为UTF-8
            std::wstring wide_name(caps.szPname);
            int size_needed = WideCharToMultiByte(CP_UTF8, 0, wide_name.c_str(), -1, NULL, 0, NULL, NULL);
            std::string utf8_name(size_needed, 0);
            WideCharToMultiByte(CP_UTF8, 0, wide_name.c_str(), -1, &utf8_name[0], size_needed, NULL, NULL);
            utf8_name.pop_back(); // 移除null终止符

            device.name = utf8_name;
            device.is_default = (i == 0);

            audio_devices.push_back(device);
        }
    }
#endif

    if (audio_devices.empty()) {
        AudioDeviceInfo default_device;
        default_device.id = -1;
        default_device.name = "默认音频设备";
        default_device.is_default = true;
        audio_devices.push_back(default_device);
    }

    debug_log("发现 " + std::to_string(audio_devices.size()) + " 个音频设备");
}

std::string WhisperGMS2Engine::get_audio_device_name(int device_id) {
    for (const auto& device : audio_devices) {
        if (device.id == device_id) {
            return device.name;
        }
    }
    return "";
}

// ============================================================================
// 音频捕获实现 (Windows)
// ============================================================================

#ifdef _WIN32
void CALLBACK WhisperGMS2Engine::wave_in_proc(HWAVEIN hwi, UINT uMsg, DWORD_PTR dwInstance,
                                              DWORD_PTR dwParam1, DWORD_PTR dwParam2) {
    WhisperGMS2Engine* engine = reinterpret_cast<WhisperGMS2Engine*>(dwInstance);

    if (uMsg == WIM_DATA) {
        WAVEHDR* header = reinterpret_cast<WAVEHDR*>(dwParam1);
        engine->handle_wave_data(header);
    }
}

void WhisperGMS2Engine::handle_wave_data(WAVEHDR* header) {
    if (!capturing.load() || !header || header->dwBytesRecorded == 0) {
        return;
    }

    // 转换音频数据格式 (16位PCM -> float)
    const int16_t* pcm_data = reinterpret_cast<const int16_t*>(header->lpData);
    size_t sample_count = header->dwBytesRecorded / sizeof(int16_t);

    std::vector<float> float_samples(sample_count);
    for (size_t i = 0; i < sample_count; ++i) {
        float_samples[i] = static_cast<float>(pcm_data[i]) / 32768.0f;
    }

    // 写入音频缓冲区
    audio_buffer->write(float_samples.data(), sample_count);

    // 重新提交缓冲区
    waveInAddBuffer(wave_in_handle, header, sizeof(WAVEHDR));
}
#endif

int WhisperGMS2Engine::start_audio_capture(int device_id) {
    if (capturing.load()) {
        debug_log("音频捕获已在运行");
        return WHISPER_GMS2_SUCCESS;
    }

    if (!initialized.load()) {
        set_error(WHISPER_GMS2_ERROR_INIT, "引擎未初始化");
        return WHISPER_GMS2_ERROR_INIT;
    }

    try {
        debug_log("开始音频捕获，设备ID: " + std::to_string(device_id));

#ifdef _WIN32
        // 设置音频格式
        WAVEFORMATEX wave_format;
        wave_format.wFormatTag = WAVE_FORMAT_PCM;
        wave_format.nChannels = channels;
        wave_format.nSamplesPerSec = sample_rate;
        wave_format.wBitsPerSample = 16;
        wave_format.nBlockAlign = wave_format.nChannels * wave_format.wBitsPerSample / 8;
        wave_format.nAvgBytesPerSec = wave_format.nSamplesPerSec * wave_format.nBlockAlign;
        wave_format.cbSize = 0;

        // 打开音频输入设备
        UINT device_id_uint = (device_id < 0) ? WAVE_MAPPER : static_cast<UINT>(device_id);
        MMRESULT result = waveInOpen(&wave_in_handle, device_id_uint, &wave_format,
                                   reinterpret_cast<DWORD_PTR>(wave_in_proc),
                                   reinterpret_cast<DWORD_PTR>(this),
                                   CALLBACK_FUNCTION);

        if (result != MMSYSERR_NOERROR) {
            set_error(WHISPER_GMS2_ERROR_AUDIO, "无法打开音频输入设备，错误代码: " + std::to_string(result));
            return WHISPER_GMS2_ERROR_AUDIO;
        }

        // 准备音频缓冲区
        const size_t buffer_size = sample_rate * channels * sizeof(int16_t) / 4; // 250ms缓冲区
        wave_buffers.resize(4);

        for (int i = 0; i < 4; ++i) {
            wave_buffers[i].resize(buffer_size);

            wave_headers[i].lpData = wave_buffers[i].data();
            wave_headers[i].dwBufferLength = static_cast<DWORD>(buffer_size);
            wave_headers[i].dwFlags = 0;
            wave_headers[i].dwLoops = 0;

            // 准备缓冲区
            result = waveInPrepareHeader(wave_in_handle, &wave_headers[i], sizeof(WAVEHDR));
            if (result != MMSYSERR_NOERROR) {
                cleanup_audio_capture();
                set_error(WHISPER_GMS2_ERROR_AUDIO, "无法准备音频缓冲区");
                return WHISPER_GMS2_ERROR_AUDIO;
            }

            // 添加缓冲区到队列
            result = waveInAddBuffer(wave_in_handle, &wave_headers[i], sizeof(WAVEHDR));
            if (result != MMSYSERR_NOERROR) {
                cleanup_audio_capture();
                set_error(WHISPER_GMS2_ERROR_AUDIO, "无法添加音频缓冲区到队列");
                return WHISPER_GMS2_ERROR_AUDIO;
            }
        }

        // 开始录音
        result = waveInStart(wave_in_handle);
        if (result != MMSYSERR_NOERROR) {
            cleanup_audio_capture();
            set_error(WHISPER_GMS2_ERROR_AUDIO, "无法开始音频录制");
            return WHISPER_GMS2_ERROR_AUDIO;
        }
#endif

        current_device_id = device_id;
        capturing.store(true);

        // 启动处理线程
        should_stop.store(false);
        processing_thread = std::thread(&WhisperGMS2Engine::processing_thread_func, this);

        debug_log("音频捕获已开始");
        return WHISPER_GMS2_SUCCESS;

    } catch (const std::exception& e) {
        set_error(WHISPER_GMS2_ERROR_AUDIO, "音频捕获启动失败: " + std::string(e.what()));
        return WHISPER_GMS2_ERROR_AUDIO;
    }
}

int WhisperGMS2Engine::stop_audio_capture() {
    if (!capturing.load()) {
        return WHISPER_GMS2_SUCCESS;
    }

    debug_log("停止音频捕获");

    capturing.store(false);
    should_stop.store(true);

    // 等待处理线程结束
    if (processing_thread.joinable()) {
        processing_thread.join();
    }

    cleanup_audio_capture();

    debug_log("音频捕获已停止");
    return WHISPER_GMS2_SUCCESS;
}

void WhisperGMS2Engine::cleanup_audio_capture() {
#ifdef _WIN32
    if (wave_in_handle) {
        waveInStop(wave_in_handle);
        waveInReset(wave_in_handle);

        // 清理缓冲区
        for (int i = 0; i < 4; ++i) {
            if (wave_headers[i].dwFlags & WHDR_PREPARED) {
                waveInUnprepareHeader(wave_in_handle, &wave_headers[i], sizeof(WAVEHDR));
            }
        }

        waveInClose(wave_in_handle);
        wave_in_handle = nullptr;
    }

    wave_buffers.clear();
    memset(wave_headers, 0, sizeof(wave_headers));
#endif
}

// ============================================================================
// 音频处理线程
// ============================================================================

void WhisperGMS2Engine::processing_thread_func() {
    debug_log("音频处理线程已启动");

    std::vector<float> processing_buffer;
    processing_buffer.reserve(WHISPER_GMS2_PROCESSING_CHUNK_SIZE);

    while (!should_stop.load()) {
        try {
            // 检查是否有足够的音频数据进行处理
            if (audio_buffer->available() >= WHISPER_GMS2_PROCESSING_CHUNK_SIZE) {
                processing_buffer.resize(WHISPER_GMS2_PROCESSING_CHUNK_SIZE);

                size_t samples_read = audio_buffer->read(processing_buffer.data(),
                                                       WHISPER_GMS2_PROCESSING_CHUNK_SIZE);

                if (samples_read > 0) {
                    processing_buffer.resize(samples_read);

                    // 检查语音活动
                    if (detect_voice_activity(processing_buffer, vad_threshold)) {
                        debug_log("检测到语音活动，开始处理");
                        process_audio_chunk(processing_buffer);
                    }
                }
            }

            // 短暂休眠避免过度占用CPU
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

        } catch (const std::exception& e) {
            set_error(WHISPER_GMS2_ERROR_PROCESSING, "音频处理线程错误: " + std::string(e.what()));
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
    }

    debug_log("音频处理线程已结束");
}

void WhisperGMS2Engine::process_audio_chunk(const std::vector<float>& audio_data) {
    if (!initialized.load() || audio_data.empty()) {
        return;
    }

    processing.store(true);

    try {
        debug_log("处理音频块，样本数: " + std::to_string(audio_data.size()));

        // 应用音频预处理
        std::vector<float> processed_audio = audio_data;
        apply_audio_preprocessing(processed_audio);

        // 调用Whisper进行语音识别
        int result = whisper_full(whisper_ctx, whisper_params,
                                processed_audio.data(),
                                static_cast<int>(processed_audio.size()));

        if (result != 0) {
            set_error(WHISPER_GMS2_ERROR_PROCESSING, "Whisper处理失败，错误代码: " + std::to_string(result));
            processing.store(false);
            return;
        }

        // 获取识别结果
        const int n_segments = whisper_full_n_segments(whisper_ctx);
        if (n_segments > 0) {
            std::string full_text;
            int64_t start_time = INT64_MAX;
            int64_t end_time = 0;

            for (int i = 0; i < n_segments; ++i) {
                const char* segment_text = whisper_full_get_segment_text(whisper_ctx, i);
                if (segment_text && strlen(segment_text) > 0) {
                    full_text += segment_text;

                    int64_t t0 = whisper_full_get_segment_t0(whisper_ctx, i);
                    int64_t t1 = whisper_full_get_segment_t1(whisper_ctx, i);

                    start_time = (t0 < start_time) ? t0 : start_time;
                    end_time = (t1 > end_time) ? t1 : end_time;
                }
            }

            // 更新识别结果
            if (!full_text.empty()) {
                std::lock_guard<std::mutex> lock(result_mutex);

                current_result.text = full_text;
                current_result.confidence = 85; // 简化的置信度计算
                current_result.start_ms = start_time * 10; // 转换为毫秒
                current_result.end_ms = end_time * 10;
                current_result.is_valid = true;

                debug_log("识别结果: " + full_text);

                // 调用结果回调
                if (result_callback) {
                    result_callback(current_result.text.c_str(),
                                  current_result.confidence,
                                  result_callback_data);
                }
            }
        }

    } catch (const std::exception& e) {
        set_error(WHISPER_GMS2_ERROR_PROCESSING, "音频处理异常: " + std::string(e.what()));
    }

    processing.store(false);
}

int WhisperGMS2Engine::process_audio(const float* audio_data, int sample_count) {
    if (!initialized.load()) {
        return WHISPER_GMS2_ERROR_INIT;
    }

    if (!audio_data || sample_count <= 0) {
        return WHISPER_GMS2_ERROR_INVALID_PARAM;
    }

    try {
        // 将音频数据写入缓冲区
        audio_buffer->write(audio_data, sample_count);

        debug_log("接收到音频数据，样本数: " + std::to_string(sample_count));

        return WHISPER_GMS2_SUCCESS;

    } catch (const std::exception& e) {
        set_error(WHISPER_GMS2_ERROR_PROCESSING, "音频数据处理失败: " + std::string(e.what()));
        return WHISPER_GMS2_ERROR_PROCESSING;
    }
}

// ============================================================================
// 结果获取函数
// ============================================================================

std::string WhisperGMS2Engine::get_result() {
    std::lock_guard<std::mutex> lock(result_mutex);

    if (current_result.is_valid) {
        return current_result.text;
    }

    return "";
}

int WhisperGMS2Engine::get_confidence() {
    std::lock_guard<std::mutex> lock(result_mutex);

    if (current_result.is_valid) {
        return current_result.confidence;
    }

    return 0;
}

int WhisperGMS2Engine::get_timestamp(int* start_ms, int* end_ms) {
    if (!start_ms || !end_ms) {
        return WHISPER_GMS2_ERROR_INVALID_PARAM;
    }

    std::lock_guard<std::mutex> lock(result_mutex);

    if (current_result.is_valid) {
        *start_ms = static_cast<int>(current_result.start_ms);
        *end_ms = static_cast<int>(current_result.end_ms);
        return WHISPER_GMS2_SUCCESS;
    }

    *start_ms = 0;
    *end_ms = 0;
    return WHISPER_GMS2_ERROR_PROCESSING;
}

int WhisperGMS2Engine::clear_result() {
    std::lock_guard<std::mutex> lock(result_mutex);
    current_result.clear();

    debug_log("识别结果已清除");

    return WHISPER_GMS2_SUCCESS;
}

// ============================================================================
// 回调函数设置
// ============================================================================

int WhisperGMS2Engine::set_result_callback(whisper_gms2_result_callback callback, void* user_data) {
    result_callback = callback;
    result_callback_data = user_data;

    debug_log("结果回调函数已设置");

    return WHISPER_GMS2_SUCCESS;
}

int WhisperGMS2Engine::set_error_callback(whisper_gms2_error_callback callback, void* user_data) {
    error_callback = callback;
    error_callback_data = user_data;

    debug_log("错误回调函数已设置");

    return WHISPER_GMS2_SUCCESS;
}

int WhisperGMS2Engine::set_debug_callback(whisper_gms2_debug_callback callback, void* user_data) {
    debug_callback = callback;
    debug_callback_data = user_data;

    debug_log("调试回调函数已设置");

    return WHISPER_GMS2_SUCCESS;
}

// ============================================================================
// 工具函数实现
// ============================================================================

const char* language_code_to_string(int language_code) {
    switch (language_code) {
        case WHISPER_GMS2_LANG_CHINESE:
            return "中文";
        case WHISPER_GMS2_LANG_ENGLISH:
            return "英文";
        case WHISPER_GMS2_LANG_AUTO:
        default:
            return "自动检测";
    }
}

int string_to_language_code(const std::string& language_str) {
    if (language_str == "zh" || language_str == "chinese" || language_str == "中文") {
        return WHISPER_GMS2_LANG_CHINESE;
    } else if (language_str == "en" || language_str == "english" || language_str == "英文") {
        return WHISPER_GMS2_LANG_ENGLISH;
    } else {
        return WHISPER_GMS2_LANG_AUTO;
    }
}

void apply_audio_preprocessing(std::vector<float>& audio_data) {
    if (audio_data.empty()) {
        return;
    }

    // 简单的音频预处理：归一化
    float max_amplitude = 0.0f;
    for (float sample : audio_data) {
        max_amplitude = (std::abs(sample) > max_amplitude) ? std::abs(sample) : max_amplitude;
    }

    if (max_amplitude > 0.0f && max_amplitude < 1.0f) {
        float scale = 0.8f / max_amplitude; // 归一化到80%最大幅度
        for (float& sample : audio_data) {
            sample *= scale;
        }
    }
}

float calculate_audio_energy(const std::vector<float>& audio_data) {
    if (audio_data.empty()) {
        return 0.0f;
    }

    float energy = 0.0f;
    for (float sample : audio_data) {
        energy += sample * sample;
    }

    return energy / static_cast<float>(audio_data.size());
}

bool detect_voice_activity(const std::vector<float>& audio_data, float threshold) {
    float energy = calculate_audio_energy(audio_data);
    return energy > threshold * threshold; // 简单的能量阈值检测
}

// ============================================================================
// DLL导出函数实现
// ============================================================================

extern "C" {

WHISPER_GMS2_API int whisper_gms2_init(const char* model_path, int language, int n_threads) {
    std::lock_guard<std::mutex> lock(g_engine_mutex);

    try {
        if (!g_engine) {
            g_engine = std::make_unique<WhisperGMS2Engine>();
        }

        return g_engine->initialize(model_path, language, n_threads);

    } catch (const std::exception& e) {
        return WHISPER_GMS2_ERROR_INIT;
    }
}

WHISPER_GMS2_API int whisper_gms2_free(void) {
    std::lock_guard<std::mutex> lock(g_engine_mutex);

    if (g_engine) {
        int result = g_engine->shutdown();
        g_engine.reset();
        return result;
    }

    return WHISPER_GMS2_SUCCESS;
}

WHISPER_GMS2_API int whisper_gms2_is_initialized(void) {
    std::lock_guard<std::mutex> lock(g_engine_mutex);

    if (g_engine) {
        return g_engine->is_initialized() ? 1 : 0;
    }

    return 0;
}

WHISPER_GMS2_API int whisper_gms2_start_capture(int device_id) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->start_audio_capture(device_id));
}

WHISPER_GMS2_API int whisper_gms2_stop_capture(void) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->stop_audio_capture());
}

WHISPER_GMS2_API int whisper_gms2_is_capturing(void) {
    if (!g_engine || !g_engine->is_initialized()) {
        return 0;
    }

    return g_engine->is_capturing() ? 1 : 0;
}

WHISPER_GMS2_API int whisper_gms2_process_audio(const float* audio_data, int sample_count) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->process_audio(audio_data, sample_count));
}

WHISPER_GMS2_API const char* whisper_gms2_get_result(void) {
    if (!g_engine || !g_engine->is_initialized()) {
        return nullptr;
    }

    static std::string result_buffer;
    result_buffer = g_engine->get_result();

    return result_buffer.empty() ? nullptr : result_buffer.c_str();
}

WHISPER_GMS2_API int whisper_gms2_get_confidence(void) {
    if (!g_engine || !g_engine->is_initialized()) {
        return 0;
    }

    return g_engine->get_confidence();
}

WHISPER_GMS2_API int whisper_gms2_get_timestamp(int* start_ms, int* end_ms) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->get_timestamp(start_ms, end_ms));
}

WHISPER_GMS2_API int whisper_gms2_clear_result(void) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->clear_result());
}

WHISPER_GMS2_API int whisper_gms2_set_language(int language) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->set_language(language));
}

WHISPER_GMS2_API int whisper_gms2_get_language(void) {
    if (!g_engine || !g_engine->is_initialized()) {
        return WHISPER_GMS2_LANG_AUTO;
    }

    return g_engine->get_language();
}

WHISPER_GMS2_API int whisper_gms2_set_audio_params(int sample_rate, int channels) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->set_audio_params(sample_rate, channels));
}

WHISPER_GMS2_API int whisper_gms2_set_processing_params(float vad_threshold, float no_speech_threshold) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->set_processing_params(vad_threshold, no_speech_threshold));
}

WHISPER_GMS2_API int whisper_gms2_set_result_callback(whisper_gms2_result_callback callback, void* user_data) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->set_result_callback(callback, user_data));
}

WHISPER_GMS2_API int whisper_gms2_set_error_callback(whisper_gms2_error_callback callback, void* user_data) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->set_error_callback(callback, user_data));
}

WHISPER_GMS2_API int whisper_gms2_set_debug_callback(whisper_gms2_debug_callback callback, void* user_data) {
    WHISPER_GMS2_CHECK_INIT();
    WHISPER_GMS2_SAFE_CALL(g_engine->set_debug_callback(callback, user_data));
}

WHISPER_GMS2_API const char* whisper_gms2_get_version(void) {
    return WHISPER_GMS2_VERSION;
}

WHISPER_GMS2_API const char* whisper_gms2_get_last_error(void) {
    if (!g_engine) {
        return "引擎未初始化";
    }

    static std::string error_buffer;
    error_buffer = g_engine->get_last_error();

    return error_buffer.empty() ? "无错误" : error_buffer.c_str();
}

WHISPER_GMS2_API int whisper_gms2_get_audio_device_count(void) {
    if (!g_engine) {
        return 0;
    }

    return g_engine->get_audio_device_count();
}

WHISPER_GMS2_API const char* whisper_gms2_get_audio_device_name(int device_id) {
    if (!g_engine) {
        return nullptr;
    }

    static std::string device_name_buffer;
    device_name_buffer = g_engine->get_audio_device_name(device_id);

    return device_name_buffer.empty() ? nullptr : device_name_buffer.c_str();
}

WHISPER_GMS2_API int whisper_gms2_set_debug_mode(int enable) {
    if (!g_engine) {
        return WHISPER_GMS2_ERROR_INIT;
    }

    return g_engine->set_debug_mode(enable != 0);
}

} // extern "C"
