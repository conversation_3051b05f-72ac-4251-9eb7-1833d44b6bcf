@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Whisper GMS2 Extension 构建脚本
echo ========================================

:: 设置变量
set BUILD_DIR=build
set CMAKE_BUILD_TYPE=Release
set ENABLE_CUDA=OFF
set ENABLE_OPENCL=OFF

:: 解析命令行参数
:parse_args
if "%~1"=="" goto :start_build
if "%~1"=="--debug" (
    set CMAKE_BUILD_TYPE=Debug
    shift
    goto :parse_args
)
if "%~1"=="--cuda" (
    set ENABLE_CUDA=ON
    shift
    goto :parse_args
)
if "%~1"=="--opencl" (
    set ENABLE_OPENCL=ON
    shift
    goto :parse_args
)
if "%~1"=="--clean" (
    echo 清理构建目录...
    if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo 用法: build.bat [选项]
    echo 选项:
    echo   --debug     构建调试版本
    echo   --cuda      启用CUDA支持
    echo   --opencl    启用OpenCL支持
    echo   --clean     清理构建目录
    echo   --help      显示此帮助信息
    exit /b 0
)
echo 未知参数: %~1
shift
goto :parse_args

:start_build

:: 检查必要工具
echo 检查构建环境...

:: 检查CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到CMake，请确保CMake已安装并在PATH中
    exit /b 1
)

:: 检查Visual Studio
where cl >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Visual Studio编译器
    echo 请运行此脚本在Visual Studio开发者命令提示符中
    echo 或者运行 "vcvarsall.bat x64" 来设置环境
    exit /b 1
)

:: 检查Whisper.cpp
if not exist "..\whisper.cpp\include\whisper.h" (
    echo 错误: 未找到Whisper.cpp库
    echo 请确保whisper.cpp目录存在于上级目录中
    exit /b 1
)

:: 检查模型文件
if not exist "..\ggml-medium.bin" (
    echo 警告: 未找到模型文件 ggml-medium.bin
    echo 请从 https://huggingface.co/ggerganov/whisper.cpp 下载模型文件
)

:: 创建构建目录
if not exist %BUILD_DIR% mkdir %BUILD_DIR%

:: 进入构建目录
cd %BUILD_DIR%

echo.
echo 构建配置:
echo   构建类型: %CMAKE_BUILD_TYPE%
echo   CUDA支持: %ENABLE_CUDA%
echo   OpenCL支持: %ENABLE_OPENCL%
echo.

:: 运行CMake配置
echo 配置项目...
cmake .. ^
    -DCMAKE_BUILD_TYPE=%CMAKE_BUILD_TYPE% ^
    -DWHISPER_GMS2_CUDA=%ENABLE_CUDA% ^
    -DWHISPER_GMS2_OPENCL=%ENABLE_OPENCL% ^
    -DBUILD_EXAMPLES=ON ^
    -G "Visual Studio 17 2022" ^
    -A x64

if errorlevel 1 (
    echo 错误: CMake配置失败
    cd ..
    exit /b 1
)

:: 构建项目
echo.
echo 开始构建...
cmake --build . --config %CMAKE_BUILD_TYPE% --parallel

if errorlevel 1 (
    echo 错误: 构建失败
    cd ..
    exit /b 1
)

:: 返回原目录
cd ..

echo.
echo ========================================
echo 构建完成!
echo ========================================
echo.
echo 输出文件位置:
echo   DLL文件: %BUILD_DIR%\%CMAKE_BUILD_TYPE%\whisper_gms2.dll
echo   库文件: %BUILD_DIR%\%CMAKE_BUILD_TYPE%\whisper_gms2.lib
echo.

:: 复制DLL到GameMaker项目
set GM_PROJECT_DIR="..\I Wanna Be The YoYoYo Edition"
if exist %GM_PROJECT_DIR% (
    echo 复制DLL到GameMaker项目...
    if not exist "%GM_PROJECT_DIR%\extensions" mkdir "%GM_PROJECT_DIR%\extensions"
    if not exist "%GM_PROJECT_DIR%\extensions\whisper_gms2" mkdir "%GM_PROJECT_DIR%\extensions\whisper_gms2"
    copy "%BUILD_DIR%\%CMAKE_BUILD_TYPE%\whisper_gms2.dll" "%GM_PROJECT_DIR%\extensions\whisper_gms2\" >nul
    echo DLL已复制到GameMaker项目
)

echo.
echo 构建脚本执行完成!
echo 如需重新构建，请运行: build.bat --clean
echo.

endlocal
