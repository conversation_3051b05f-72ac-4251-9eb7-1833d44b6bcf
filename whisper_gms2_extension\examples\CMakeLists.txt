# 示例程序CMakeLists.txt

# 简单测试程序
add_executable(whisper_gms2_test
    test_basic.cpp
)

target_link_libraries(whisper_gms2_test
    whisper_gms2
)

target_include_directories(whisper_gms2_test PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
)

# 设置输出目录
set_target_properties(whisper_gms2_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/${CMAKE_BUILD_TYPE}
)

# Windows特定设置
if(WIN32)
    # 复制DLL到示例程序目录
    add_custom_command(TARGET whisper_gms2_test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:whisper_gms2>
        $<TARGET_FILE_DIR:whisper_gms2_test>
    )
endif()
