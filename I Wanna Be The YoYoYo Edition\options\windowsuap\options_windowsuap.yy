{
  "option_windowsuap_display_name": "Created with GameMaker Studio 2",
  "option_windowsuap_package_name": "YourPackageName",
  "option_windowsuap_publisher_display_name": "YourPublisherName",
  "option_windowsuap_package_display_name": "YourPackageDisplayName",
  "option_windowsuap_description": "Your Description",
  "option_windowsuap_version": "*******",
  "option_windowsuap_orient_portrait": true,
  "option_windowsuap_orient_portrait_flipped": true,
  "option_windowsuap_orient_landscape": true,
  "option_windowsuap_orient_landscape_flipped": true,
  "option_windowsuap_small_logo": "${base_options_dir}/windowsuap/logos/SmallLogo.scale-100.png",
  "option_windowsuap_smallish_logo": "${base_options_dir}/windowsuap/logos/SmallishLogo.scale-100.png",
  "option_windowsuap_store_logo": "${base_options_dir}/windowsuap/logos/StoreLogo.scale-100.png",
  "option_windowsuap_logo": "${base_options_dir}/windowsuap/logos/Logo.scale-100.png",
  "option_windowsuap_logo_background_colour": 4278190080,
  "option_windowsuap_logo_foreground_text": 0,
  "option_windowsuap_wide_logo": "${base_options_dir}/windowsuap/logos/WideLogo.scale-100.png",
  "option_windowsuap_large_logo": "${base_options_dir}/windowsuap/logos/LargeLogo.scale-100.png",
  "option_windowsuap_splash_png": "${base_options_dir}/windowsuap/splash/SplashScreen.scale-100.png",
  "option_windowsuap_splash_background_colour": 4278190080,
  "option_windowsuap_interpolate_pixels": false,
  "option_windowsuap_display_cursor": true,
  "option_windowsuap_start_fullscreen": false,
  "option_windowsuap_allow_fullscreen_switching": false,
  "option_windowsuap_use_synchronization": true,
  "option_windowsuap_scale": 0,
  "option_windowsuap_texture_page": "2048x2048",
  "option_windowsuap_certificate_location": "${base_options_dir}\\windowsuap\\keys\\WinUWPRunner_TemporaryKey.pfx",
  "option_windowsuap_certificate_publishername": "CN=Sandbox",
  "option_windowsuap_native_cpu": 0,
  "option_windowsuap_internet_capable": false,
  "option_windowsuap_microphone_capable": false,
  "option_windowsuap_iap_sandbox": false,
  "option_windowsuap_targetdevicefamily_universal": true,
  "option_windowsuap_target_platform_version": "10.0.14393.0",
  "option_windowsuap_target_platform_min_version": "10.0.14393.0",
  "option_windowsuap_targetdevicefamily_desktop": false,
  "option_windowsuap_desktop_family_platform_version": "10.0.14393.0",
  "option_windowsuap_desktop_family_platform_min_version": "10.0.14393.0",
  "option_windowsuap_targetdevicefamily_xbox": false,
  "option_windowsuap_xbox_family_platform_version": "10.0.14393.0",
  "option_windowsuap_xbox_family_platform_min_version": "10.0.14393.0",
  "option_windowsuap_targetdevicefamily_mobile": false,
  "option_windowsuap_mobile_family_platform_version": "10.0.14393.0",
  "option_windowsuap_mobile_family_platform_min_version": "10.0.14393.0",
  "option_windowsuap_targetdevicefamily_holographic": false,
  "option_windowsuap_holographic_family_platform_version": "10.0.14393.0",
  "option_windowsuap_holographic_family_platform_min_version": "10.0.14393.0",
  "option_windowsuap_targetdevicefamily_team": false,
  "option_windowsuap_team_family_platform_version": "10.0.14393.0",
  "option_windowsuap_team_family_platform_min_version": "10.0.14393.0",
  "option_windowsuap_xbox_live": false,
  "option_windowsuap_xbox_live_creators_program": false,
  "option_windowsuap_xbox_live_title_id": "0",
  "option_windowsuap_xbox_live_scid": "00000000-0000-0000-0000-000000000000",
  "resourceVersion": "1.0",
  "name": "Windows UWP",
  "tags": [],
  "resourceType": "GMWindowsUAPOptions",
}