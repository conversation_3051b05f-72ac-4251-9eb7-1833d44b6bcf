{
  "option_html5_browser_title": "Created with GameMaker Studio 2",
  "option_html5_version": "1.0.0.0",
  "option_html5_foldername": "html5game",
  "option_html5_outputname": "index.html",
  "option_html5_splash_png": "${base_options_dir}/html5/splash.png",
  "option_html5_usesplash": false,
  "option_html5_outputdebugtoconsole": true,
  "option_html5_display_cursor": true,
  "option_html5_localrunalert": true,
  "option_html5_index": "",
  "option_html5_loadingbar": "",
  "option_html5_jsprepend": "",
  "option_html5_icon": "${base_options_dir}/html5/fav.ico",
  "option_html5_allow_fullscreen": true,
  "option_html5_interpolate_pixels": true,
  "option_html5_centregame": false,
  "option_html5_usebuiltinparticles": true,
  "option_html5_usebuiltinfont": true,
  "option_html5_webgl": 2,
  "option_html5_scale": 0,
  "option_html5_texture_page": "2048x2048",
  "option_html5_use_facebook": false,
  "option_html5_facebook_id": "",
  "option_html5_facebook_app_display_name": "",
  "option_html5_flurry_enable": false,
  "option_html5_flurry_id": "",
  "option_html5_google_analytics_enable": false,
  "option_html5_google_tracking_id": "",
  "resourceVersion": "1.0",
  "name": "HTML5",
  "tags": [],
  "resourceType": "GMHtml5Options",
}