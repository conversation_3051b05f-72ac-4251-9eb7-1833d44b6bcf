{
  "option_operagx_version": "1.0.0.0",
  "option_operagx_next_version": "1.0.0.0",
  "option_operagx_game_name": "${project_name}",
  "option_operagx_interpolate_pixels": true,
  "option_operagx_scale": 0,
  "option_operagx_texture_page": "2048x2048",
  "option_operagx_display_cursor": true,
  "option_operagx_guid": "",
  "option_operagx_team_name": "",
  "option_operagx_team_id": "",
  "option_operagx_editUrl": "",
  "option_operagx_internalShareUrl": "",
  "option_operagx_publicShareUrl": "",
  "resourceVersion": "1.0",
  "name": "operagx",
  "tags": [],
  "resourceType": "GMOperaGXOptions",
}