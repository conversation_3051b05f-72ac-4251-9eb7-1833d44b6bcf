{"resourceType": "GMExtension", "resourceVersion": "1.2", "name": "whisper_gms2", "androidactivityinject": "", "androidclassname": "", "androidcodeinjection": "", "androidinject": "", "androidmanifestinject": "", "androidPermissions": [], "androidProps": false, "androidsourcedir": "", "author": "Whisper GMS2 Extension", "classname": "", "copyToTargets": 113497714299118, "description": "实时语音识别扩展，基于OpenAI Whisper C++库，支持中英文语音识别", "exportToGame": true, "extensionVersion": "1.0.0", "files": [{"resourceType": "GMExtensionFile", "resourceVersion": "1.0", "name": "whisper_gms2.dll", "constants": [], "copyToTargets": 9223372036854775807, "filename": "whisper_gms2.dll", "final": "", "functions": [{"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_init", "argCount": 3, "args": [1, 2, 2], "documentation": "初始化Whisper语音识别引擎\n@param {string} model_path 模型文件路径\n@param {real} language 语言设置 (0=自动, 1=中文, 2=英文)\n@param {real} n_threads 线程数 (0=自动检测)\n@return {real} 成功返回0，失败返回错误代码", "externalName": "whisper_gms2_init", "help": "whisper_gms2_init(model_path, language, n_threads)", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_free", "argCount": 0, "args": [], "documentation": "释放Whisper语音识别引擎资源\n@return {real} 成功返回0", "externalName": "whisper_gms2_free", "help": "whisper_gms2_free()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_is_initialized", "argCount": 0, "args": [], "documentation": "检查引擎是否已初始化\n@return {real} 1=已初始化，0=未初始化", "externalName": "whisper_gms2_is_initialized", "help": "whisper_gms2_is_initialized()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_start_capture", "argCount": 1, "args": [2], "documentation": "开始实时音频捕获\n@param {real} device_id 音频设备ID (-1=默认设备)\n@return {real} 成功返回0，失败返回错误代码", "externalName": "whisper_gms2_start_capture", "help": "whisper_gms2_start_capture(device_id)", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_stop_capture", "argCount": 0, "args": [], "documentation": "停止实时音频捕获\n@return {real} 成功返回0", "externalName": "whisper_gms2_stop_capture", "help": "whisper_gms2_stop_capture()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_is_capturing", "argCount": 0, "args": [], "documentation": "检查音频捕获状态\n@return {real} 1=正在捕获，0=未捕获", "externalName": "whisper_gms2_is_capturing", "help": "whisper_gms2_is_capturing()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_get_result", "argCount": 0, "args": [], "documentation": "获取最新的识别结果\n@return {string} 识别文本字符串，无结果返回空字符串", "externalName": "whisper_gms2_get_result", "help": "whisper_gms2_get_result()", "hidden": false, "kind": 11, "returnType": 1}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_get_confidence", "argCount": 0, "args": [], "documentation": "获取最新识别结果的置信度\n@return {real} 置信度 (0-100)", "externalName": "whisper_gms2_get_confidence", "help": "whisper_gms2_get_confidence()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_clear_result", "argCount": 0, "args": [], "documentation": "清除当前识别结果\n@return {real} 成功返回0", "externalName": "whisper_gms2_clear_result", "help": "whisper_gms2_clear_result()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_set_language", "argCount": 1, "args": [2], "documentation": "设置识别语言\n@param {real} language 语言代码 (0=自动, 1=中文, 2=英文)\n@return {real} 成功返回0", "externalName": "whisper_gms2_set_language", "help": "whisper_gms2_set_language(language)", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_get_language", "argCount": 0, "args": [], "documentation": "获取当前语言设置\n@return {real} 当前语言代码", "externalName": "whisper_gms2_get_language", "help": "whisper_gms2_get_language()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_get_version", "argCount": 0, "args": [], "documentation": "获取版本信息\n@return {string} 版本字符串", "externalName": "whisper_gms2_get_version", "help": "whisper_gms2_get_version()", "hidden": false, "kind": 11, "returnType": 1}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_get_last_error", "argCount": 0, "args": [], "documentation": "获取最后的错误信息\n@return {string} 错误信息字符串", "externalName": "whisper_gms2_get_last_error", "help": "whisper_gms2_get_last_error()", "hidden": false, "kind": 11, "returnType": 1}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_get_audio_device_count", "argCount": 0, "args": [], "documentation": "获取可用的音频设备数量\n@return {real} 设备数量", "externalName": "whisper_gms2_get_audio_device_count", "help": "whisper_gms2_get_audio_device_count()", "hidden": false, "kind": 11, "returnType": 2}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_get_audio_device_name", "argCount": 1, "args": [2], "documentation": "获取音频设备名称\n@param {real} device_id 设备ID\n@return {string} 设备名称字符串，失败返回空字符串", "externalName": "whisper_gms2_get_audio_device_name", "help": "whisper_gms2_get_audio_device_name(device_id)", "hidden": false, "kind": 11, "returnType": 1}, {"resourceType": "GMExtensionFunction", "resourceVersion": "1.0", "name": "whisper_gms2_set_debug_mode", "argCount": 1, "args": [2], "documentation": "启用/禁用调试模式\n@param {real} enable 1=启用，0=禁用\n@return {real} 成功返回0", "externalName": "whisper_gms2_set_debug_mode", "help": "whisper_gms2_set_debug_mode(enable)", "hidden": false, "kind": 11, "returnType": 2}], "init": "", "kind": 1, "order": [], "origname": "", "ProxyFiles": [], "uncompress": false, "usesRunnerInterface": false}], "gradleinject": "", "hasConvertedCodeInjection": true, "helpfile": "", "HTML5CodeInjection": "", "html5Props": false, "IncludedResources": [], "installdir": "", "iosCocoaPodDependencies": "", "iosCocoaPods": "", "ioscodeinjection": "", "iosdelegatename": "", "iosplistinject": "", "iosProps": false, "iosSystemFrameworkEntries": [], "iosThirdPartyFrameworkEntries": [], "license": "MIT License", "maccompilerflags": "", "maclinkerflags": "", "macsourcedir": "", "options": [], "optionsFile": "options.json", "packageId": "com.whisper.gms2.extension", "parent": {"name": "Extensions", "path": "folders/Extensions.yy"}, "productId": "", "sourcedir": "", "supportedTargets": 113497714299118, "tvosclassname": "", "tvosCocoaPodDependencies": "", "tvosCocoaPods": "", "tvoscodeinjection": "", "tvosdelegatename": "", "tvosmaccompilerflags": "", "tvosmaclinkerflags": "", "tvosplistinject": "", "tvosProps": false, "tvosSystemFrameworkEntries": [], "tvosThirdPartyFrameworkEntries": []}