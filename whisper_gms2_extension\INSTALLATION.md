# Whisper GMS2 Extension 安装指南

本指南将帮助您在GameMaker Studio 2项目中安装和配置Whisper语音识别扩展。

## 前置要求

### 系统要求
- **操作系统**: Windows 10/11 (64位)
- **GameMaker Studio**: 2022.1 或更高版本
- **Visual C++ Redistributable**: 2019 或更高版本
- **内存**: 建议4GB以上RAM
- **存储空间**: 至少5GB可用空间（包括模型文件）

### 开发环境（仅构建时需要）
- Visual Studio 2019/2022 with C++ 工具
- CMake 3.16 或更高版本
- Git

## 安装步骤

### 方法1: 使用预编译版本（推荐）

1. **下载扩展文件**
   - 从Release页面下载最新版本的 `whisper_gms2_extension.zip`
   - 解压到临时目录

2. **复制DLL文件**
   ```
   将 whisper_gms2.dll 复制到你的GameMaker项目目录下的:
   extensions/whisper_gms2/
   ```

3. **导入扩展到GameMaker**
   - 打开GameMaker Studio 2
   - 在资源树中右键点击 "Extensions"
   - 选择 "Import Extension"
   - 选择 `whisper_gms2.yy` 文件
   - 点击 "Import"

4. **下载模型文件**
   - 访问 [Hugging Face Whisper模型页面](https://huggingface.co/ggerganov/whisper.cpp)
   - 下载推荐的模型文件（建议使用 `ggml-medium.bin`）
   - 将模型文件放置在项目根目录的 `models/` 文件夹中

### 方法2: 从源码构建

1. **克隆仓库**
   ```bash
   git clone --recursive https://github.com/your-repo/whisper-gms2-extension.git
   cd whisper-gms2-extension
   ```

2. **构建扩展**
   ```bash
   # 使用构建脚本
   cd whisper_gms2_extension
   build.bat
   
   # 或者手动构建
   mkdir build
   cd build
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
   ```

3. **复制构建结果**
   ```
   将 build/Release/whisper_gms2.dll 复制到GameMaker项目的扩展目录
   ```

## 配置GameMaker项目

### 1. 导入GML脚本

将以下脚本文件导入到你的GameMaker项目中：
- `whisper_speech_recognition.gml` - 主要的GML封装函数
- `example_usage.gml` - 使用示例（可选）

### 2. 创建初始化对象

创建一个持久对象（如 `obj_whisper_manager`）来管理语音识别：

```gml
// Create事件
if (whisper_init("models/ggml-medium.bin", WHISPER_LANG_CHINESE)) {
    show_debug_message("Whisper初始化成功");
    whisper_start_capture();
} else {
    show_debug_message("Whisper初始化失败: " + whisper_get_last_error());
}

// Step事件
var speech_result = whisper_process_step();
if (speech_result != "") {
    // 处理语音识别结果
    show_debug_message("识别结果: " + speech_result);
    whisper_clear_result();
}

// Clean Up事件
whisper_cleanup();
```

### 3. 设置项目属性

在GameMaker Studio中：
1. 打开 "Game Options"
2. 选择 "Windows"
3. 在 "General" 标签下确保：
   - Architecture 设置为 "x64"
   - 启用 "Use External Tools"

## 模型文件配置

### 推荐模型选择

| 用途 | 模型文件 | 大小 | 内存需求 | 精度 |
|------|----------|------|----------|------|
| 开发测试 | `ggml-tiny.bin` | ~39MB | ~125MB | 低 |
| 轻量应用 | `ggml-base.bin` | ~142MB | ~210MB | 中 |
| 一般应用 | `ggml-small.bin` | ~466MB | ~600MB | 中高 |
| 高精度需求 | `ggml-medium.bin` | ~1.5GB | ~2.6GB | 高 |
| 专业应用 | `ggml-large.bin` | ~2.9GB | ~4.7GB | 最高 |

### 模型文件放置

推荐的目录结构：
```
YourGameProject/
├── models/
│   ├── ggml-medium.bin
│   └── ggml-small.bin (备用)
├── extensions/
│   └── whisper_gms2/
│       ├── whisper_gms2.dll
│       └── whisper_gms2.yy
└── scripts/
    ├── whisper_speech_recognition/
    └── example_usage/
```

## 验证安装

### 1. 基本功能测试

创建一个测试房间，添加以下代码：

```gml
// 在某个对象的Create事件中
show_debug_message("Whisper版本: " + whisper_get_version());

if (whisper_init("models/ggml-medium.bin", WHISPER_LANG_CHINESE)) {
    show_debug_message("✓ 初始化成功");
    
    var devices = whisper_get_audio_devices();
    show_debug_message("发现 " + string(array_length(devices)) + " 个音频设备");
    
    if (whisper_start_capture()) {
        show_debug_message("✓ 音频捕获已开始");
    }
} else {
    show_debug_message("✗ 初始化失败: " + whisper_get_last_error());
}
```

### 2. 音频设备测试

```gml
// 列出所有可用的音频设备
var devices = whisper_get_audio_devices();
for (var i = 0; i < array_length(devices); i++) {
    show_debug_message("设备 " + string(devices[i].id) + ": " + devices[i].name);
}
```

### 3. 语音识别测试

```gml
// 在Step事件中
var result = whisper_process_step();
if (result != "") {
    show_debug_message("识别结果: " + result);
    show_debug_message("置信度: " + string(whisper_get_confidence()) + "%");
    whisper_clear_result();
}
```

## 常见问题解决

### 问题1: DLL加载失败

**症状**: GameMaker启动时报错 "无法加载whisper_gms2.dll"

**解决方案**:
1. 确保DLL文件在正确的路径
2. 安装Visual C++ Redistributable 2019
3. 检查DLL是否为64位版本
4. 使用Dependency Walker检查DLL依赖

### 问题2: 初始化失败

**症状**: `whisper_init()` 返回false

**解决方案**:
1. 检查模型文件路径是否正确
2. 确保模型文件完整下载（检查文件大小）
3. 检查系统内存是否足够
4. 查看错误信息：`whisper_get_last_error()`

### 问题3: 音频捕获失败

**症状**: `whisper_start_capture()` 返回false

**解决方案**:
1. 检查麦克风权限设置
2. 确认音频设备正常工作
3. 尝试使用不同的设备ID
4. 检查是否有其他程序占用音频设备

### 问题4: 识别精度低

**症状**: 语音识别结果不准确

**解决方案**:
1. 使用更大的模型文件
2. 确保环境安静，减少背景噪音
3. 调整麦克风音量和位置
4. 检查语言设置是否正确

### 问题5: 性能问题

**症状**: 游戏运行缓慢或卡顿

**解决方案**:
1. 使用较小的模型文件
2. 调整线程数设置
3. 启用硬件加速（如果支持）
4. 在性能较好的设备上运行

## 高级配置

### 启用CUDA加速

如果你有NVIDIA GPU，可以启用CUDA加速：

1. 下载支持CUDA的版本
2. 确保安装了CUDA Toolkit
3. 使用CUDA版本的DLL文件

### 自定义音频参数

```gml
// 设置音频采样率和声道数
whisper_gms2_set_audio_params(16000, 1); // 16kHz, 单声道

// 设置处理参数
whisper_gms2_set_processing_params(0.6, 0.6); // VAD阈值, 静音阈值
```

### 回调函数设置

```gml
// 注意：回调函数需要在C++层面实现
// 这里仅作为接口说明
whisper_gms2_set_result_callback(callback_function, user_data);
whisper_gms2_set_error_callback(error_callback_function, user_data);
```

## 技术支持

如果遇到问题，请：

1. 查看调试输出信息
2. 检查 `whisper_get_last_error()` 的返回值
3. 在GitHub Issues中搜索类似问题
4. 提供详细的错误信息和系统配置

## 更新说明

### 更新扩展

1. 下载新版本的DLL文件
2. 替换项目中的旧DLL文件
3. 检查是否有新的API变更
4. 测试现有功能是否正常

### 版本兼容性

- 向后兼容：新版本通常兼容旧版本的API
- 重大更新：会在发布说明中标明不兼容的变更
- 建议：在更新前备份项目文件

---

安装完成后，您就可以开始在I Wanna游戏中使用语音识别功能了！查看 `example_usage.gml` 获取更多使用示例。
