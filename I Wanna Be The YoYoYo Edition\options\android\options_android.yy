{
  "option_android_sync_amazon": false,
  "option_android_display_name": "Created with GameMaker Studio 2",
  "option_android_version": "1.0.0.0",
  "option_android_tools_from_version": false,
  "option_android_build_tools": "",
  "option_android_support_lib": "",
  "option_android_target_sdk": "",
  "option_android_minimum_sdk": "",
  "option_android_compile_sdk": "",
  "option_android_package_domain": "com",
  "option_android_package_company": "company",
  "option_android_package_product": "game",
  "option_android_arch_armv7": true,
  "option_android_arch_x86": false,
  "option_android_arch_arm64": false,
  "option_android_arch_x86_64": false,
  "option_android_orient_portrait": true,
  "option_android_orient_portrait_flipped": true,
  "option_android_orient_landscape": true,
  "option_android_orient_landscape_flipped": true,
  "option_android_gamepad_support": true,
  "option_android_lint": false,
  "option_android_install_location": 0,
  "option_android_sleep_margin": 4,
  "option_android_splash_screens_landscape": "${base_options_dir}/android/splash/landscape.png",
  "option_android_splash_screens_portrait": "${base_options_dir}/android/splash/portrait.png",
  "option_android_splash_time": 0,
  "option_android_launchscreen_fill": 0,
  "option_android_splashscreen_background_colour": 255,
  "option_android_tv_banner": "${base_options_dir}/android/tv_banner.png",
  "option_android_interpolate_pixels": false,
  "option_android_screen_depth": 0,
  "option_android_device_support": 0,
  "option_android_scale": 0,
  "option_android_texture_page": "2048x2048",
  "option_android_icon_ldpi": "${base_options_dir}/android/icons/ldpi.png",
  "option_android_icon_mdpi": "${base_options_dir}/android/icons/mdpi.png",
  "option_android_icon_hdpi": "${base_options_dir}/android/icons/hdpi.png",
  "option_android_icon_xhdpi": "${base_options_dir}/android/icons/xhdpi.png",
  "option_android_icon_xxhdpi": "${base_options_dir}/android/icons/xxhdpi.png",
  "option_android_icon_xxxhdpi": "${base_options_dir}/android/icons/xxxhdpi.png",
  "option_android_icon_adaptive_generate": false,
  "option_android_icon_adaptive_ldpi": "${base_options_dir}/android/icons_adaptive/ldpi.png",
  "option_android_icon_adaptive_mdpi": "${base_options_dir}/android/icons_adaptive/mdpi.png",
  "option_android_icon_adaptive_hdpi": "${base_options_dir}/android/icons_adaptive/hdpi.png",
  "option_android_icon_adaptive_xhdpi": "${base_options_dir}/android/icons_adaptive/xhdpi.png",
  "option_android_icon_adaptive_xxhdpi": "${base_options_dir}/android/icons_adaptive/xxhdpi.png",
  "option_android_icon_adaptive_xxxhdpi": "${base_options_dir}/android/icons_adaptive/xxxhdpi.png",
  "option_android_icon_adaptivebg_ldpi": "${base_options_dir}/android/icons_adaptivebg/ldpi.png",
  "option_android_icon_adaptivebg_mdpi": "${base_options_dir}/android/icons_adaptivebg/mdpi.png",
  "option_android_icon_adaptivebg_hdpi": "${base_options_dir}/android/icons_adaptivebg/hdpi.png",
  "option_android_icon_adaptivebg_xhdpi": "${base_options_dir}/android/icons_adaptivebg/xhdpi.png",
  "option_android_icon_adaptivebg_xxhdpi": "${base_options_dir}/android/icons_adaptivebg/xxhdpi.png",
  "option_android_icon_adaptivebg_xxxhdpi": "${base_options_dir}/android/icons_adaptivebg/xxxhdpi.png",
  "option_android_use_facebook": false,
  "option_android_facebook_id": "",
  "option_android_facebook_app_display_name": "",
  "option_android_google_cloud_saving": false,
  "option_android_google_services_app_id": "",
  "option_android_permission_write_external_storage": false,
  "option_android_permission_read_phone_state": false,
  "option_android_permission_network_state": false,
  "option_android_permission_internet": true,
  "option_android_permission_bluetooth": true,
  "option_android_permission_record_audio": false,
  "option_android_application_tag_inject": "",
  "option_android_google_apk_expansion": false,
  "option_android_google_dynamic_asset_delivery": false,
  "option_android_google_licensing_public_key": "",
  "option_android_tv_isgame": true,
  "resourceVersion": "1.0",
  "name": "Android",
  "tags": [],
  "resourceType": "GMAndroidOptions",
}