{
  "resourceType": "GMRoom",
  "resourceVersion": "1.0",
  "name": "rTitle",
  "isDnd": false,
  "volume": 1.0,
  "parentRoom": null,
  "views": [
    {"inherit":false,"visible":true,"xview":0,"yview":0,"wview":800,"hview":608,"xport":0,"yport":0,"wport":800,"hport":608,"hborder":400,"vborder":304,"hspeed":-1,"vspeed":-1,"objectId":null,},
    {"inherit":false,"visible":false,"xview":0,"yview":0,"wview":1024,"hview":768,"xport":0,"yport":0,"wport":1024,"hport":768,"hborder":32,"vborder":32,"hspeed":-1,"vspeed":-1,"objectId":null,},
    {"inherit":false,"visible":false,"xview":0,"yview":0,"wview":1024,"hview":768,"xport":0,"yport":0,"wport":1024,"hport":768,"hborder":32,"vborder":32,"hspeed":-1,"vspeed":-1,"objectId":null,},
    {"inherit":false,"visible":false,"xview":0,"yview":0,"wview":1024,"hview":768,"xport":0,"yport":0,"wport":1024,"hport":768,"hborder":32,"vborder":32,"hspeed":-1,"vspeed":-1,"objectId":null,},
    {"inherit":false,"visible":false,"xview":0,"yview":0,"wview":1024,"hview":768,"xport":0,"yport":0,"wport":1024,"hport":768,"hborder":32,"vborder":32,"hspeed":-1,"vspeed":-1,"objectId":null,},
    {"inherit":false,"visible":false,"xview":0,"yview":0,"wview":1024,"hview":768,"xport":0,"yport":0,"wport":1024,"hport":768,"hborder":32,"vborder":32,"hspeed":-1,"vspeed":-1,"objectId":null,},
    {"inherit":false,"visible":false,"xview":0,"yview":0,"wview":1024,"hview":768,"xport":0,"yport":0,"wport":1024,"hport":768,"hborder":32,"vborder":32,"hspeed":-1,"vspeed":-1,"objectId":null,},
    {"inherit":false,"visible":false,"xview":0,"yview":0,"wview":1024,"hview":768,"xport":0,"yport":0,"wport":1024,"hport":768,"hborder":32,"vborder":32,"hspeed":-1,"vspeed":-1,"objectId":null,},
  ],
  "layers": [
    {"resourceType":"GMRInstanceLayer","resourceVersion":"1.0","name":"World","instances":[
        {"resourceType":"GMRInstance","resourceVersion":"1.0","name":"inst_1F267A39","properties":[],"isDnd":false,"objectId":{"name":"objTitleMenu","path":"objects/objTitleMenu/objTitleMenu.yy",},"inheritCode":false,"hasCreationCode":false,"colour":4294967295,"rotation":0.0,"scaleX":1.0,"scaleY":1.0,"imageIndex":0,"imageSpeed":1.0,"inheritedItemId":null,"frozen":false,"ignore":false,"inheritItemSettings":false,"x":0.0,"y":0.0,},
      ],"visible":true,"depth":0,"userdefinedDepth":false,"inheritLayerDepth":false,"inheritLayerSettings":false,"gridX":32,"gridY":32,"layers":[],"hierarchyFrozen":false,"effectEnabled":true,"effectType":null,"properties":[],},
    {"resourceType":"GMRTileLayer","resourceVersion":"1.1","name":"Tiles","tilesetId":{"name":"tAllTiles","path":"tilesets/tAllTiles/tAllTiles.yy",},"x":0,"y":0,"tiles":{"TileDataFormat":1,"SerialiseWidth":25,"SerialiseHeight":19,"TileCompressedData":[
-76,-2147483648,1,2,-6,-2147483648,1,2,-11,-2147483648,1,2,-5,-2147483648,-2,2,-4,-2147483648,-2,2,-5,-2147483648,1,2,-4,
-2147483648,-2,2,-6,-2147483648,1,2,-3,-2147483648,-2,2,-6,-2147483648,5,2,-2147483648,-2147483648,2,2,-8,-2147483648,5,2,-2147483648,-2147483648,
2,2,-3,-2147483648,-2,2,-3,-2147483648,3,2,-2147483648,2,-9,-2147483648,-3,2,-3,-2147483648,-2,2,2,-2147483648,2,-3,-2147483648,
-2,2,-4,-2147483648,-2,2,-6,-2147483648,1,2,-3,-2147483648,4,2,-2147483648,-2147483648,2,-3,-2147483648,-2,2,-3,-2147483648,-2,2,
2,-2147483648,2,-5,-2147483648,1,2,-3,-2147483648,4,2,-2147483648,-2147483648,2,-3,-2147483648,1,2,-3,-2147483648,1,2,-3,-2147483648,1,
2,-4,-2147483648,1,2,-3,-2147483648,1,2,-3,-2147483648,1,2,-3,-2147483648,1,2,-3,-2147483648,1,2,-3,-2147483648,1,2,
-4,-2147483648,1,2,-3,-2147483648,-2,2,2,-2147483648,2,-3,-2147483648,1,2,-4,-2147483648,5,2,-2147483648,-2147483648,2,2,-4,-2147483648,
1,2,-4,-2147483648,-2,2,-4,-2147483648,1,2,-4,-2147483648,-4,2,-4,-2147483648,1,2,-10,-2147483648,1,2,-136,-2147483648,],},"visible":true,"depth":100,"userdefinedDepth":false,"inheritLayerDepth":false,"inheritLayerSettings":false,"gridX":32,"gridY":32,"layers":[],"hierarchyFrozen":false,"effectEnabled":true,"effectType":null,"properties":[],},
    {"resourceType":"GMRBackgroundLayer","resourceVersion":"1.0","name":"Background","spriteId":null,"colour":4294967168,"x":0,"y":0,"htiled":false,"vtiled":false,"hspeed":0.0,"vspeed":0.0,"stretch":false,"animationFPS":15.0,"animationSpeedType":0,"userdefinedAnimFPS":false,"visible":true,"depth":200,"userdefinedDepth":false,"inheritLayerDepth":false,"inheritLayerSettings":false,"gridX":32,"gridY":32,"layers":[],"hierarchyFrozen":false,"effectEnabled":true,"effectType":null,"properties":[],},
  ],
  "inheritLayers": false,
  "creationCodeFile": "",
  "inheritCode": false,
  "instanceCreationOrder": [
    {"name":"inst_1F267A39","path":"rooms/rTitle/rTitle.yy",},
  ],
  "inheritCreationOrder": false,
  "sequenceId": null,
  "roomSettings": {
    "inheritRoomSettings": false,
    "Width": 800,
    "Height": 608,
    "persistent": false,
  },
  "viewSettings": {
    "inheritViewSettings": false,
    "enableViews": true,
    "clearViewBackground": true,
    "clearDisplayBuffer": true,
  },
  "physicsSettings": {
    "inheritPhysicsSettings": false,
    "PhysicsWorld": false,
    "PhysicsWorldGravityX": 0.0,
    "PhysicsWorldGravityY": 10.0,
    "PhysicsWorldPixToMetres": 0.1,
  },
  "parent": {
    "name": "Init",
    "path": "folders/Rooms/Init.yy",
  },
}