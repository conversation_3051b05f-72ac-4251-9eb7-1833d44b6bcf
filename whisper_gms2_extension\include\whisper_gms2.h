#ifndef WHISPER_GMS2_H
#define WHISPER_GMS2_H

#ifdef __cplusplus
extern "C" {
#endif

// Windows DLL导出宏定义
#ifdef _WIN32
    #ifdef WHISPER_GMS2_EXPORTS
        #define WHISPER_GMS2_API __declspec(dllexport)
    #else
        #define WHISPER_GMS2_API __declspec(dllimport)
    #endif
#else
    #define WHISPER_GMS2_API
#endif

// 错误代码定义
#define WHISPER_GMS2_SUCCESS           0
#define WHISPER_GMS2_ERROR_INIT        -1
#define WHISPER_GMS2_ERROR_MODEL       -2
#define WHISPER_GMS2_ERROR_AUDIO       -3
#define WHISPER_GMS2_ERROR_PROCESSING  -4
#define WHISPER_GMS2_ERROR_MEMORY      -5
#define WHISPER_GMS2_ERROR_INVALID_PARAM -6

// 语言代码定义
#define WHISPER_GMS2_LANG_AUTO         0
#define WHISPER_GMS2_LANG_CHINESE      1
#define WHISPER_GMS2_LANG_ENGLISH      2

// 音频参数定义
#define WHISPER_GMS2_SAMPLE_RATE       16000
#define WHISPER_GMS2_CHANNELS          1
#define WHISPER_GMS2_BUFFER_SIZE       4096

// 回调函数类型定义
typedef void (*whisper_gms2_result_callback)(const char* text, int confidence, void* user_data);
typedef void (*whisper_gms2_error_callback)(int error_code, const char* error_message, void* user_data);
typedef void (*whisper_gms2_debug_callback)(const char* debug_message, void* user_data);

// ============================================================================
// 核心API函数
// ============================================================================

/**
 * 初始化Whisper语音识别引擎
 * @param model_path 模型文件路径 (ggml格式)
 * @param language 语言设置 (WHISPER_GMS2_LANG_*)
 * @param n_threads 线程数 (0=自动检测)
 * @return 成功返回WHISPER_GMS2_SUCCESS，失败返回错误代码
 */
WHISPER_GMS2_API int whisper_gms2_init(const char* model_path, int language, int n_threads);

/**
 * 释放Whisper语音识别引擎资源
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_free(void);

/**
 * 检查引擎是否已初始化
 * @return 1=已初始化，0=未初始化
 */
WHISPER_GMS2_API int whisper_gms2_is_initialized(void);

// ============================================================================
// 音频捕获和处理API
// ============================================================================

/**
 * 开始实时音频捕获
 * @param device_id 音频设备ID (-1=默认设备)
 * @return 成功返回WHISPER_GMS2_SUCCESS，失败返回错误代码
 */
WHISPER_GMS2_API int whisper_gms2_start_capture(int device_id);

/**
 * 停止实时音频捕获
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_stop_capture(void);

/**
 * 检查音频捕获状态
 * @return 1=正在捕获，0=未捕获
 */
WHISPER_GMS2_API int whisper_gms2_is_capturing(void);

/**
 * 处理音频数据进行语音识别
 * @param audio_data 音频数据 (float数组，16kHz单声道)
 * @param sample_count 样本数量
 * @return 成功返回WHISPER_GMS2_SUCCESS，失败返回错误代码
 */
WHISPER_GMS2_API int whisper_gms2_process_audio(const float* audio_data, int sample_count);

// ============================================================================
// 结果获取API
// ============================================================================

/**
 * 获取最新的识别结果
 * @return 识别文本字符串指针 (UTF-8编码)，无结果返回NULL
 */
WHISPER_GMS2_API const char* whisper_gms2_get_result(void);

/**
 * 获取最新识别结果的置信度
 * @return 置信度 (0-100)
 */
WHISPER_GMS2_API int whisper_gms2_get_confidence(void);

/**
 * 获取识别结果的时间戳
 * @param start_ms 输出开始时间 (毫秒)
 * @param end_ms 输出结束时间 (毫秒)
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_get_timestamp(int* start_ms, int* end_ms);

/**
 * 清除当前识别结果
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_clear_result(void);

// ============================================================================
// 配置API
// ============================================================================

/**
 * 设置识别语言
 * @param language 语言代码 (WHISPER_GMS2_LANG_*)
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_set_language(int language);

/**
 * 获取当前语言设置
 * @return 当前语言代码
 */
WHISPER_GMS2_API int whisper_gms2_get_language(void);

/**
 * 设置音频参数
 * @param sample_rate 采样率 (建议16000)
 * @param channels 声道数 (建议1)
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_set_audio_params(int sample_rate, int channels);

/**
 * 设置处理参数
 * @param vad_threshold VAD阈值 (0.0-1.0)
 * @param no_speech_threshold 静音阈值 (0.0-1.0)
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_set_processing_params(float vad_threshold, float no_speech_threshold);

// ============================================================================
// 回调函数API
// ============================================================================

/**
 * 设置识别结果回调函数
 * @param callback 回调函数指针
 * @param user_data 用户数据指针
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_set_result_callback(whisper_gms2_result_callback callback, void* user_data);

/**
 * 设置错误回调函数
 * @param callback 回调函数指针
 * @param user_data 用户数据指针
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_set_error_callback(whisper_gms2_error_callback callback, void* user_data);

/**
 * 设置调试回调函数
 * @param callback 回调函数指针
 * @param user_data 用户数据指针
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_set_debug_callback(whisper_gms2_debug_callback callback, void* user_data);

// ============================================================================
// 工具函数API
// ============================================================================

/**
 * 获取版本信息
 * @return 版本字符串
 */
WHISPER_GMS2_API const char* whisper_gms2_get_version(void);

/**
 * 获取最后的错误信息
 * @return 错误信息字符串 (UTF-8编码)
 */
WHISPER_GMS2_API const char* whisper_gms2_get_last_error(void);

/**
 * 获取可用的音频设备数量
 * @return 设备数量
 */
WHISPER_GMS2_API int whisper_gms2_get_audio_device_count(void);

/**
 * 获取音频设备名称
 * @param device_id 设备ID
 * @return 设备名称字符串，失败返回NULL
 */
WHISPER_GMS2_API const char* whisper_gms2_get_audio_device_name(int device_id);

/**
 * 启用/禁用调试模式
 * @param enable 1=启用，0=禁用
 * @return 成功返回WHISPER_GMS2_SUCCESS
 */
WHISPER_GMS2_API int whisper_gms2_set_debug_mode(int enable);

#ifdef __cplusplus
}
#endif

#endif // WHISPER_GMS2_H
