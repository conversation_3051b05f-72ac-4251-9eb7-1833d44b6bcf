cmake_minimum_required(VERSION 3.16)

project(whisper_gms2_extension VERSION 1.0.0 LANGUAGES C CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 编译选项
if(MSVC)
    add_compile_options(/W3 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(NOMINMAX)
    add_compile_definitions(WIN32_LEAN_AND_MEAN)
else()
    add_compile_options(-Wall -Wextra -O3)
endif()

# 查找依赖包
find_package(Threads REQUIRED)

# 设置Whisper.cpp路径
set(WHISPER_CPP_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../whisper.cpp")

# 检查Whisper.cpp是否存在
if(NOT EXISTS "${WHISPER_CPP_DIR}")
    message(FATAL_ERROR "Whisper.cpp directory not found at ${WHISPER_CPP_DIR}")
endif()

# 添加Whisper.cpp子目录
add_subdirectory("${WHISPER_CPP_DIR}" whisper_cpp_build)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${WHISPER_CPP_DIR}/include
    ${WHISPER_CPP_DIR}/ggml/include
)

# 源文件
set(WHISPER_GMS2_SOURCES
    src/whisper_gms2.cpp
)

# 头文件
set(WHISPER_GMS2_HEADERS
    include/whisper_gms2.h
    include/whisper_gms2_internal.h
)

# 创建共享库 (DLL)
add_library(whisper_gms2 SHARED
    ${WHISPER_GMS2_SOURCES}
    ${WHISPER_GMS2_HEADERS}
)

# 设置目标属性
set_target_properties(whisper_gms2 PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    OUTPUT_NAME "whisper_gms2"
    PREFIX ""
)

# Windows特定设置
if(WIN32)
    set_target_properties(whisper_gms2 PROPERTIES
        SUFFIX ".dll"
    )
    
    # 导出所有符号
    set_target_properties(whisper_gms2 PROPERTIES
        WINDOWS_EXPORT_ALL_SYMBOLS ON
    )
endif()

# 链接库
target_link_libraries(whisper_gms2
    whisper
    ggml
    Threads::Threads
)

# Windows特定链接库
if(WIN32)
    target_link_libraries(whisper_gms2
        winmm
    )
endif()

# 编译定义
target_compile_definitions(whisper_gms2 PRIVATE
    WHISPER_GMS2_EXPORTS
    GGML_USE_CPU
)

# 如果支持CUDA
option(WHISPER_GMS2_CUDA "Enable CUDA support" OFF)
if(WHISPER_GMS2_CUDA)
    find_package(CUDAToolkit)
    if(CUDAToolkit_FOUND)
        target_compile_definitions(whisper_gms2 PRIVATE GGML_USE_CUDA)
        target_link_libraries(whisper_gms2 CUDA::cudart CUDA::cublas)
    endif()
endif()

# 如果支持OpenCL
option(WHISPER_GMS2_OPENCL "Enable OpenCL support" OFF)
if(WHISPER_GMS2_OPENCL)
    find_package(OpenCL)
    if(OpenCL_FOUND)
        target_compile_definitions(whisper_gms2 PRIVATE GGML_USE_OPENCL)
        target_link_libraries(whisper_gms2 OpenCL::OpenCL)
    endif()
endif()

# 安装规则
install(TARGETS whisper_gms2
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES ${WHISPER_GMS2_HEADERS}
    DESTINATION include
)

# 创建模块定义文件 (Windows)
if(WIN32)
    set(DEF_FILE "${CMAKE_CURRENT_BINARY_DIR}/whisper_gms2.def")
    file(WRITE ${DEF_FILE} "EXPORTS\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_init\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_free\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_is_initialized\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_start_capture\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_stop_capture\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_is_capturing\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_process_audio\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_result\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_confidence\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_timestamp\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_clear_result\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_set_language\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_language\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_set_audio_params\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_set_processing_params\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_set_result_callback\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_set_error_callback\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_set_debug_callback\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_version\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_last_error\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_audio_device_count\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_get_audio_device_name\n")
    file(APPEND ${DEF_FILE} "whisper_gms2_set_debug_mode\n")
    
    set_target_properties(whisper_gms2 PROPERTIES
        LINK_FLAGS "/DEF:${DEF_FILE}"
    )
endif()

# 打印配置信息
message(STATUS "Whisper GMS2 Extension Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Whisper.cpp path: ${WHISPER_CPP_DIR}")
message(STATUS "  CUDA support: ${WHISPER_GMS2_CUDA}")
message(STATUS "  OpenCL support: ${WHISPER_GMS2_OPENCL}")

# 创建示例程序
option(BUILD_EXAMPLES "Build example programs" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 创建测试程序
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()
