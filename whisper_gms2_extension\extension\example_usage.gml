/// @description Whisper语音识别扩展使用示例
/// 这个脚本展示了如何在I Wanna类游戏中使用语音识别功能

// ============================================================================
// 示例1: 基础语音识别设置
// ============================================================================

/// @function example_basic_setup
/// @description 基础设置示例 - 在游戏开始时调用
function example_basic_setup() {
    show_debug_message("=== Whisper语音识别基础设置示例 ===");
    
    // 初始化语音识别引擎
    var model_path = "models/ggml-medium.bin"; // 确保模型文件存在
    
    if (whisper_init(model_path, WHISPER_LANG_CHINESE)) {
        show_debug_message("✓ 语音识别引擎初始化成功!");
        show_debug_message("版本: " + whisper_get_version());
        
        // 启用调试模式
        whisper_enable_debug(true);
        
        // 显示可用的音频设备
        var devices = whisper_get_audio_devices();
        show_debug_message("发现 " + string(array_length(devices)) + " 个音频设备:");
        for (var i = 0; i < array_length(devices); i++) {
            show_debug_message("  设备 " + string(devices[i].id) + ": " + devices[i].name);
        }
        
        // 开始音频捕获
        if (whisper_start_capture()) {
            show_debug_message("✓ 音频捕获已开始");
            return true;
        } else {
            show_debug_message("✗ 音频捕获启动失败: " + whisper_get_last_error());
            return false;
        }
    } else {
        show_debug_message("✗ 语音识别引擎初始化失败: " + whisper_get_last_error());
        return false;
    }
}

// ============================================================================
// 示例2: I Wanna游戏语音控制
// ============================================================================

/// @function example_iwanna_voice_control
/// @description I Wanna游戏语音控制示例 - 在Step事件中调用
function example_iwanna_voice_control() {
    // 检查是否有新的语音识别结果
    var speech_result = whisper_process_step();
    
    if (speech_result != "") {
        var confidence = whisper_get_confidence();
        show_debug_message("语音识别: " + speech_result + " (置信度: " + string(confidence) + "%)");
        
        // 只处理高置信度的结果
        if (confidence >= 70) {
            // 转换为小写便于匹配
            var speech_lower = string_lower(speech_result);
            
            // I Wanna游戏常用语音命令
            if (string_pos("跳跃", speech_result) > 0 || string_pos("jump", speech_lower) > 0) {
                show_debug_message("执行跳跃命令");
                // 执行跳跃动作
                if (instance_exists(obj_player)) {
                    with (obj_player) {
                        if (place_meeting(x, y + 1, obj_block)) {
                            vspeed = -jump_speed;
                        }
                    }
                }
            }
            
            else if (string_pos("向左", speech_result) > 0 || string_pos("left", speech_lower) > 0) {
                show_debug_message("执行向左移动命令");
                if (instance_exists(obj_player)) {
                    with (obj_player) {
                        hspeed = -move_speed;
                    }
                }
            }
            
            else if (string_pos("向右", speech_result) > 0 || string_pos("right", speech_lower) > 0) {
                show_debug_message("执行向右移动命令");
                if (instance_exists(obj_player)) {
                    with (obj_player) {
                        hspeed = move_speed;
                    }
                }
            }
            
            else if (string_pos("停止", speech_result) > 0 || string_pos("stop", speech_lower) > 0) {
                show_debug_message("执行停止命令");
                if (instance_exists(obj_player)) {
                    with (obj_player) {
                        hspeed = 0;
                        vspeed = 0;
                    }
                }
            }
            
            else if (string_pos("重新开始", speech_result) > 0 || string_pos("restart", speech_lower) > 0) {
                show_debug_message("执行重新开始命令");
                room_restart();
            }
            
            else if (string_pos("暂停", speech_result) > 0 || string_pos("pause", speech_lower) > 0) {
                show_debug_message("执行暂停命令");
                global.game_paused = !global.game_paused;
            }
            
            else if (string_pos("保存", speech_result) > 0 || string_pos("save", speech_lower) > 0) {
                show_debug_message("执行保存命令");
                // 执行游戏保存逻辑
                game_save("savefile.dat");
            }
        }
        
        // 清除结果以避免重复处理
        whisper_clear_result();
    }
}

// ============================================================================
// 示例3: 语音菜单导航
// ============================================================================

/// @function example_voice_menu_navigation
/// @description 语音菜单导航示例
function example_voice_menu_navigation() {
    var speech_result = whisper_process_step();
    
    if (speech_result != "") {
        var confidence = whisper_get_confidence();
        
        if (confidence >= 60) {
            var speech_lower = string_lower(speech_result);
            
            // 菜单导航命令
            if (string_pos("开始游戏", speech_result) > 0 || string_pos("start", speech_lower) > 0) {
                show_debug_message("语音选择: 开始游戏");
                room_goto(rm_game);
            }
            
            else if (string_pos("设置", speech_result) > 0 || string_pos("settings", speech_lower) > 0) {
                show_debug_message("语音选择: 设置");
                room_goto(rm_settings);
            }
            
            else if (string_pos("退出", speech_result) > 0 || string_pos("quit", speech_lower) > 0) {
                show_debug_message("语音选择: 退出游戏");
                game_end();
            }
            
            else if (string_pos("上一个", speech_result) > 0 || string_pos("up", speech_lower) > 0) {
                show_debug_message("语音导航: 上一个选项");
                // 菜单向上导航逻辑
                global.menu_selection = max(0, global.menu_selection - 1);
            }
            
            else if (string_pos("下一个", speech_result) > 0 || string_pos("down", speech_lower) > 0) {
                show_debug_message("语音导航: 下一个选项");
                // 菜单向下导航逻辑
                global.menu_selection = min(global.menu_max_options - 1, global.menu_selection + 1);
            }
            
            else if (string_pos("确认", speech_result) > 0 || string_pos("select", speech_lower) > 0) {
                show_debug_message("语音确认: 选择当前选项");
                // 执行当前菜单选项
                execute_menu_option(global.menu_selection);
            }
        }
        
        whisper_clear_result();
    }
}

// ============================================================================
// 示例4: 动态语言切换
// ============================================================================

/// @function example_language_switching
/// @description 动态语言切换示例
function example_language_switching() {
    var speech_result = whisper_process_step();
    
    if (speech_result != "") {
        var confidence = whisper_get_confidence();
        
        if (confidence >= 70) {
            // 检测语言切换命令
            if (string_pos("切换到中文", speech_result) > 0 || string_pos("switch to chinese", string_lower(speech_result)) > 0) {
                if (whisper_set_language(WHISPER_LANG_CHINESE)) {
                    show_debug_message("已切换到中文识别模式");
                    global.current_language = "中文";
                }
            }
            
            else if (string_pos("切换到英文", speech_result) > 0 || string_pos("switch to english", string_lower(speech_result)) > 0) {
                if (whisper_set_language(WHISPER_LANG_ENGLISH)) {
                    show_debug_message("已切换到英文识别模式");
                    global.current_language = "English";
                }
            }
            
            else if (string_pos("自动检测", speech_result) > 0 || string_pos("auto detect", string_lower(speech_result)) > 0) {
                if (whisper_set_language(WHISPER_LANG_AUTO)) {
                    show_debug_message("已切换到自动语言检测模式");
                    global.current_language = "自动检测";
                }
            }
        }
        
        whisper_clear_result();
    }
}

// ============================================================================
// 示例5: 语音识别状态监控
// ============================================================================

/// @function example_status_monitoring
/// @description 语音识别状态监控示例 - 在Draw GUI事件中调用
function example_status_monitoring() {
    // 绘制语音识别状态信息
    var status_y = 10;
    var line_height = 20;
    
    draw_set_color(c_white);
    draw_set_font(fnt_debug);
    
    // 基本状态信息
    draw_text(10, status_y, "=== Whisper语音识别状态 ===");
    status_y += line_height;
    
    draw_text(10, status_y, "版本: " + whisper_get_version());
    status_y += line_height;
    
    draw_text(10, status_y, "初始化状态: " + (whisper_is_initialized() ? "已初始化" : "未初始化"));
    status_y += line_height;
    
    draw_text(10, status_y, "捕获状态: " + (whisper_is_capturing() ? "正在捕获" : "未捕获"));
    status_y += line_height;
    
    draw_text(10, status_y, "当前语言: " + whisper_get_language_name());
    status_y += line_height;
    
    // 最新识别结果
    if (global.whisper_last_result != "") {
        draw_text(10, status_y, "最新结果: " + global.whisper_last_result);
        status_y += line_height;
        
        draw_text(10, status_y, "置信度: " + string(global.whisper_last_confidence) + "%");
        status_y += line_height;
    }
    
    // 错误信息
    var last_error = whisper_get_last_error();
    if (last_error != "无错误" && last_error != "") {
        draw_set_color(c_red);
        draw_text(10, status_y, "错误: " + last_error);
        draw_set_color(c_white);
        status_y += line_height;
    }
    
    // 音频设备信息
    if (whisper_is_initialized()) {
        var devices = whisper_get_audio_devices();
        draw_text(10, status_y, "音频设备数量: " + string(array_length(devices)));
        status_y += line_height;
    }
}

// ============================================================================
// 示例6: 清理资源
// ============================================================================

/// @function example_cleanup
/// @description 资源清理示例 - 在游戏结束时调用
function example_cleanup() {
    show_debug_message("=== 清理Whisper语音识别资源 ===");
    
    if (whisper_is_initialized()) {
        // 停止音频捕获
        if (whisper_is_capturing()) {
            whisper_stop_capture();
            show_debug_message("✓ 音频捕获已停止");
        }
        
        // 清理引擎资源
        if (whisper_cleanup()) {
            show_debug_message("✓ 语音识别引擎资源已清理");
        } else {
            show_debug_message("✗ 资源清理失败: " + whisper_get_last_error());
        }
    }
    
    show_debug_message("=== 资源清理完成 ===");
}
