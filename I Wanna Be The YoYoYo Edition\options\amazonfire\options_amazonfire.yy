{
  "option_amazonfire_sync_android": false,
  "option_amazonfire_display_name": "Created with GameMaker Studio 2",
  "option_amazonfire_version": "*******",
  "option_amazonfire_tools_from_version": false,
  "option_amazonfire_build_tools": "",
  "option_amazonfire_support_lib": "",
  "option_amazonfire_target_sdk": "",
  "option_amazonfire_minimum_sdk": "",
  "option_amazonfire_compile_sdk": "",
  "option_amazonfire_package_domain": "com",
  "option_amazonfire_package_company": "company",
  "option_amazonfire_package_product": "game",
  "option_amazonfire_orient_portrait": true,
  "option_amazonfire_orient_portrait_flipped": true,
  "option_amazonfire_orient_landscape": true,
  "option_amazonfire_orient_landscape_flipped": true,
  "option_amazonfire_gamepad_support": true,
  "option_amazonfire_lint": false,
  "option_amazonfire_install_location": 0,
  "option_amazonfire_sleep_margin": 4,
  "option_amazonfire_splash_screens_landscape": "${base_options_dir}/amazonfire/splash/landscape.png",
  "option_amazonfire_splash_screens_portrait": "${base_options_dir}/amazonfire/splash/portrait.png",
  "option_amazonfire_splash_time": 0,
  "option_amazonfire_launchscreen_fill": 0,
  "option_amazonfire_splashscreen_background_colour": 255,
  "option_amazonfire_tv_banner": "${base_options_dir}/amazonfire/tv_banner.png",
  "option_amazonfire_interpolate_pixels": false,
  "option_amazonfire_screen_depth": 0,
  "option_amazonfire_scale": 0,
  "option_amazonfire_texture_page": "2048x2048",
  "option_amazonfire_icon_ldpi": "${base_options_dir}/android/icons/ldpi.png",
  "option_amazonfire_icon_mdpi": "${base_options_dir}/android/icons/mdpi.png",
  "option_amazonfire_icon_hdpi": "${base_options_dir}/android/icons/hdpi.png",
  "option_amazonfire_icon_xhdpi": "${base_options_dir}/android/icons/xhdpi.png",
  "option_amazonfire_icon_xxhdpi": "${base_options_dir}/android/icons/xxhdpi.png",
  "option_amazonfire_icon_xxxhdpi": "${base_options_dir}/android/icons/xxxhdpi.png",
  "option_amazonfire_permission_write_external_storage": false,
  "option_amazonfire_permission_read_phone_state": false,
  "option_amazonfire_permission_network_state": false,
  "option_amazonfire_permission_internet": true,
  "option_amazonfire_permission_bluetooth": true,
  "option_amazonfire_permission_record_audio": false,
  "option_amazonfire_application_tag_inject": "",
  "resourceVersion": "1.0",
  "name": "Amazon Fire",
  "tags": [],
  "resourceType": "GMAmazonFireOptions",
}