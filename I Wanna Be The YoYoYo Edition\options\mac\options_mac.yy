{
  "option_mac_display_name": "Created with GameMaker Studio 2",
  "option_mac_app_id": "com.company.game",
  "option_mac_version": "1.0.0.0",
  "option_mac_output_dir": "~/gamemakerstudio2",
  "option_mac_team_id": "",
  "option_mac_signing_identity": "Developer ID Application:",
  "option_mac_copyright": "",
  "option_mac_splash_png": "${base_options_dir}/mac/splash/splash.png",
  "option_mac_icon_png": "${base_options_dir}/mac/icons/1024.png",
  "option_mac_installer_background_png": "${base_options_dir}/mac/splash/installer_background.png",
  "option_mac_menu_dock": false,
  "option_mac_display_cursor": true,
  "option_mac_start_fullscreen": false,
  "option_mac_allow_fullscreen": false,
  "option_mac_interpolate_pixels": true,
  "option_mac_vsync": false,
  "option_mac_resize_window": false,
  "option_mac_enable_retina": false,
  "option_mac_scale": 0,
  "option_mac_texture_page": "2048x2048",
  "option_mac_build_app_store": false,
  "option_mac_allow_incoming_network": false,
  "option_mac_allow_outgoing_network": false,
  "option_mac_app_category": "Games",
  "option_mac_enable_steam": false,
  "option_mac_disable_sandbox": false,
  "option_mac_apple_sign_in": false,
  "resourceVersion": "1.0",
  "name": "macOS",
  "tags": [],
  "resourceType": "GMMacOptions",
}