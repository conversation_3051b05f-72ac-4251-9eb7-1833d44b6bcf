#ifndef WHISPER_GMS2_INTERNAL_H
#define WHISPER_GMS2_INTERNAL_H

#include "whisper_gms2.h"
#include "whisper.h"
#include <memory>
#include <string>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <condition_variable>

#ifdef _WIN32
#include <windows.h>
#include <mmsystem.h>
#pragma comment(lib, "winmm.lib")
#endif

// 内部常量定义
#define WHISPER_GMS2_VERSION "1.0.0"
#define WHISPER_GMS2_MAX_ERROR_MSG_LEN 512
#define WHISPER_GMS2_MAX_RESULT_LEN 4096
#define WHISPER_GMS2_AUDIO_BUFFER_SIZE 16000 * 2  // 2秒缓冲
#define WHISPER_GMS2_PROCESSING_CHUNK_SIZE 16000 * 3  // 3秒处理块

// 音频设备信息结构
struct AudioDeviceInfo {
    int id;
    std::string name;
    bool is_default;
};

// 音频缓冲区结构
struct AudioBuffer {
    std::vector<float> data;
    size_t write_pos;
    size_t read_pos;
    std::mutex mutex;
    
    AudioBuffer(size_t size) : data(size), write_pos(0), read_pos(0) {}
    
    void write(const float* samples, size_t count);
    size_t read(float* samples, size_t count);
    size_t available() const;
    void clear();
};

// 识别结果结构
struct RecognitionResult {
    std::string text;
    int confidence;
    int64_t start_ms;
    int64_t end_ms;
    bool is_valid;
    
    RecognitionResult() : confidence(0), start_ms(0), end_ms(0), is_valid(false) {}
    
    void clear() {
        text.clear();
        confidence = 0;
        start_ms = 0;
        end_ms = 0;
        is_valid = false;
    }
};

// 主要的Whisper GMS2引擎类
class WhisperGMS2Engine {
private:
    // Whisper相关
    struct whisper_context* whisper_ctx;
    struct whisper_full_params whisper_params;
    
    // 状态管理
    std::atomic<bool> initialized;
    std::atomic<bool> capturing;
    std::atomic<bool> processing;
    std::atomic<bool> should_stop;
    
    // 配置参数
    int current_language;
    int sample_rate;
    int channels;
    int n_threads;
    float vad_threshold;
    float no_speech_threshold;
    bool debug_mode;
    
    // 音频处理
    std::unique_ptr<AudioBuffer> audio_buffer;
    std::thread capture_thread;
    std::thread processing_thread;
    std::vector<AudioDeviceInfo> audio_devices;
    int current_device_id;
    
#ifdef _WIN32
    HWAVEIN wave_in_handle;
    WAVEHDR wave_headers[4];
    std::vector<std::vector<char>> wave_buffers;
#endif
    
    // 结果管理
    RecognitionResult current_result;
    std::mutex result_mutex;
    
    // 回调函数
    whisper_gms2_result_callback result_callback;
    void* result_callback_data;
    whisper_gms2_error_callback error_callback;
    void* error_callback_data;
    whisper_gms2_debug_callback debug_callback;
    void* debug_callback_data;
    
    // 错误处理
    std::string last_error;
    std::mutex error_mutex;
    
public:
    // 内部方法（需要被外部访问）
    void set_error(int error_code, const std::string& message);
    void debug_log(const std::string& message);

private:
    // 私有内部方法
    void init_whisper_params();
    void init_audio_devices();
    void capture_thread_func();
    void processing_thread_func();
    void process_audio_chunk(const std::vector<float>& audio_data);
    void cleanup_audio_capture();
    
#ifdef _WIN32
    static void CALLBACK wave_in_proc(HWAVEIN hwi, UINT uMsg, DWORD_PTR dwInstance, 
                                     DWORD_PTR dwParam1, DWORD_PTR dwParam2);
    void handle_wave_data(WAVEHDR* header);
#endif

public:
    WhisperGMS2Engine();
    ~WhisperGMS2Engine();
    
    // 核心功能
    int initialize(const char* model_path, int language, int n_threads);
    int shutdown();
    bool is_initialized() const { return initialized.load(); }
    
    // 音频捕获
    int start_audio_capture(int device_id);
    int stop_audio_capture();
    bool is_capturing() const { return capturing.load(); }
    
    // 音频处理
    int process_audio(const float* audio_data, int sample_count);
    
    // 结果获取
    std::string get_result();
    int get_confidence();
    int get_timestamp(int* start_ms, int* end_ms);
    int clear_result();
    
    // 配置
    int set_language(int language);
    int get_language() const { return current_language; }
    int set_audio_params(int sample_rate, int channels);
    int set_processing_params(float vad_threshold, float no_speech_threshold);
    
    // 回调设置
    int set_result_callback(whisper_gms2_result_callback callback, void* user_data);
    int set_error_callback(whisper_gms2_error_callback callback, void* user_data);
    int set_debug_callback(whisper_gms2_debug_callback callback, void* user_data);
    
    // 工具函数
    std::string get_last_error();
    int get_audio_device_count() const { return static_cast<int>(audio_devices.size()); }
    std::string get_audio_device_name(int device_id);
    int set_debug_mode(bool enable);
};

// 全局引擎实例
extern std::unique_ptr<WhisperGMS2Engine> g_engine;
extern std::mutex g_engine_mutex;

// 内部工具函数
const char* language_code_to_string(int language_code);
int string_to_language_code(const std::string& language_str);
std::vector<float> convert_audio_format(const void* input_data, size_t input_size, 
                                       int input_format, int input_sample_rate, 
                                       int input_channels);
void apply_audio_preprocessing(std::vector<float>& audio_data);
float calculate_audio_energy(const std::vector<float>& audio_data);
bool detect_voice_activity(const std::vector<float>& audio_data, float threshold);

// 错误处理宏
#define WHISPER_GMS2_CHECK_INIT() \
    do { \
        if (!g_engine || !g_engine->is_initialized()) { \
            return WHISPER_GMS2_ERROR_INIT; \
        } \
    } while(0)

#define WHISPER_GMS2_SAFE_CALL(call) \
    do { \
        try { \
            return call; \
        } catch (const std::exception& e) { \
            if (g_engine) { \
                g_engine->set_error(WHISPER_GMS2_ERROR_PROCESSING, e.what()); \
            } \
            return WHISPER_GMS2_ERROR_PROCESSING; \
        } catch (...) { \
            if (g_engine) { \
                g_engine->set_error(WHISPER_GMS2_ERROR_PROCESSING, "未知错误"); \
            } \
            return WHISPER_GMS2_ERROR_PROCESSING; \
        } \
    } while(0)

#endif // WHISPER_GMS2_INTERNAL_H
